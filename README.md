# Video Action Annotation Pipeline

This repository contains a two-stage video action annotation pipeline that uses AI vision models to generate detailed annotations for videos. The pipeline consists of two main scripts that should be run in sequence:

1. `seed_batch_standard_multi.py` - Generates coarse-grained (standard) annotations
2. `seed_batch_dense_multi.py` - Generates fine-grained (dense) annotations based on the coarse annotations

## System Requirements

- Python 3.7+
- OpenCV
- NumPy
- OpenAI Python client
- tqdm (for progress bars)

## Pipeline Overview

### Stage 1: Coarse-Grained Annotation

The first stage uses `seed_batch_standard_multi.py` to process videos and generate coarse-grained annotations that identify key actions in the videos. These annotations include timestamps and descriptions of major actions.

### Stage 2: Fine-Grained Annotation

The second stage uses `seed_batch_dense_multi.py` to process the same videos but with reference to the coarse-grained annotations from Stage 1. It breaks down each coarse action into multiple fine-grained sub-actions with precise timestamps.

## Configuration

Both scripts use a similar configuration structure:

- API keys are stored in an array at the top of each script
- Video processing parameters are defined in the `VIDEO_CONFIG` dictionary
- Prompt templates are loaded from text files in the `prompt` directory

## Usage Instructions

### Step 1: Run Coarse-Grained Annotation

```bash
python seed_batch_standard_multi.py [video_directory_path]
```

If no path is provided, the script will use the default path defined in the script.

#### Key Parameters:

- `log_folder`: Directory where results will be saved (default: `seed_annotion/results/results_standard_t`)
- `prompts_folder`: Directory containing prompt templates (default: `seed_annotion/prompt/standard`)
- `base_video_path`: Base path for video files

The script will:
1. Load prompt templates from the specified directory
2. Recursively find all video files in the specified directory
3. Process videos in parallel using multiple API keys
4. Save results as JSON files in the output directory

### Step 2: Run Fine-Grained Annotation

```bash
python seed_batch_dense_multi.py [video_directory_path]
```

#### Key Parameters:

- `log_folder`: Directory where dense results will be saved (default: `seed_annotion/results/results_dense_t`)
- `standard_base_path`: Directory containing coarse-grained results from Step 1 (default: `seed_annotion/results/results_standard_t`)
- `prompts_folder`: Directory containing prompt templates (default: `seed_annotion/prompt/dense`)
- `video_base_path`: Base path for video files

The script will:
1. Load prompt templates from the specified directory
2. Find all video files in the specified directory
3. For each video, load the corresponding coarse-grained annotation from Step 1
4. Process videos in parallel to generate fine-grained annotations
5. Save results as JSON files in the output directory

## Prompt Templates

Both scripts use prompt templates to guide the AI model in generating annotations:

- **Standard prompts** (`prompt/standard/`): Used for coarse-grained annotation
- **Dense prompts** (`prompt/dense/`): Used for fine-grained annotation

Each prompt template can contain placeholders:
- For standard prompts: No specific placeholders
- For dense prompts: `{title}` and `{coarse_input}` placeholders that will be filled with the video title and coarse annotations

## Output Format

Both scripts generate JSON files with the following structure:

```json
{
  "video_name": {
    "video_name": "example_video",
    "title": "Person folding a shirt",
    "actions": [
      {
        "start_frame": 0,
        "end_frame": 150,
        "description": "flatten the crumpled shirt on the table",
        "fps": 30
      },
      ...
    ],
    "raw_output": "Original text output from the model",
    "model": "model_name",
    "fps": 30
  }
}
```

## Important Notes

1. The scripts use multiple API keys to process videos in parallel for better throughput
2. Temporary directories are created for frame extraction and are cleaned up after processing
3. Failed videos and errors are logged for troubleshooting
4. The scripts maintain the same directory structure in the output as in the input

## Error Handling

Both scripts generate failure logs that record any errors encountered during processing. These logs are saved in the output directory as `failure_log.txt`.

## Performance

Processing speed depends on:
- Number of available API keys
- Video resolution and length
- Number of frames extracted per video
- Server response time

With multiple API keys, the scripts can process multiple videos simultaneously, significantly improving throughput.
