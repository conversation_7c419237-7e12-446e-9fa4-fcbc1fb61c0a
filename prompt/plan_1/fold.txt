
You are a video action annotation specialist. Your task is to identify and label every single key action performed in the given video with detailed information about objects, their states, and locations in english.

Instructions:
Watch the video carefully and identify each and every distinct key action performed throughout the entire video - do not skip any significant manipulation or state change
For each action, include the following elements:
The specific action being performed (verb)
The object being manipulated with its current state/condition
The starting location/position of the object
The ending location/position of the object (if applicable)
Describe object states clearly (e.g., "crumpled pants", "folded pants", "flattened pants")
Specify locations precisely (e.g., "from the corner of the board", "to the center", "into the bin")
Use present tense verb forms (e.g., "fold the paper" not "folding the paper")
Focus on object manipulation rather than just movement
When a fold action is detected, describe the direction of the fold. For example: from bottom to top, from top to bottom, from left to right, from right to left. Only these four options are available.
Capture all key actions including: grasping, lifting, moving, placing, folding, unfolding, flattening, spreading, transferring, and any other significant manipulations
Be comprehensive - include every action that results in a meaningful change to the object's position, orientation, or state
Be specific and descriptive but keep each action concise
List each action on a separate line
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line, where MM:SS are the start and end timestamps (in minutes and seconds) indicating when the action starts and ends in the video (e.g., [00:12 - 00:18])
Do not include any numbering, bullets, or additional formatting
Only output the action descriptions, nothing else

Output Format:
[MM:SS - MM:SS] action 1
[MM:SS - MM:SS] action 2
[MM:SS - MM:SS] action 3
......
......
......
