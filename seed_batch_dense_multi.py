from typing import Optional, Dict
from enum import Enum
import os
import base64
import shutil
import json
from datetime import datetime
import re
import queue
import concurrent.futures
import random
import threading
import time
import math
import logging
import tempfile
import contextlib

import cv2
import numpy as np
from openai import OpenAI
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tqdm import tqdm

# 设置日志级别
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 添加多API密钥支持
API_KEYS = [
    "aea10b88-e545-4a39-94a6-dcfe260455c6",
    "18b8133d-ffdf-46e7-a805-dd6a65fc465e",
    "fa111cfa-0ea6-4050-9973-4f87759fcb41",
    "07a1edf3-df75-4f44-9b6e-20f6c28887d5",
    "03f08ef9-1bb2-472d-9360-2c0cfbccefd5",    
    "3fe079be-2fa4-493e-bf9c-eec8e5315f48",
    "b69a3181-336f-4121-b10c-c94390cfdcc0",
    "90989f87-e5e2-4f33-a1ed-07b7d4064ba8",
    "11fbfe30-5c81-42ee-81f8-0c0878be0bd4",
    "872b18f5-7e18-4600-be87-03114d1224e0",
    # 在此添加其他API密钥
]

# Video processing configuration
VIDEO_CONFIG = {
    'sampling_fps': 1,
    'max_frames': 30,
    'sampling_interval': 1.0,  # 1.0 / sampling_fps
    'model_name': "doubao-1-5-thinking-vision-pro-250428",
    'max_workers': len(API_KEYS)  # 使用所有可用的API密钥
}

# 创建OpenAI客户端池
CLIENTS = []
for api_key in API_KEYS:
    client = OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key
    )
    CLIENTS.append(client)
    print(f"已创建OpenAI客户端: {api_key[:10]}...")

# 线程安全的打印函数
print_lock = threading.Lock()

# Dictionary to store prompts
PROMPTS = {}

# 添加临时文件配置
TEMP_CONFIG = {
    'temp_base_dir': os.path.join(os.path.dirname(os.path.abspath(__file__)), 'temp')
}

def print_safe(message):
    with print_lock:
        print(message)

def load_prompts_from_directory(prompts_dir: str) -> Dict[str, str]:
    """Load prompts from text files in the specified directory.
    
    Args:
        prompts_dir (str): Path to directory containing prompt files
        
    Returns:
        Dict[str, str]: Dictionary mapping folder names to prompts
    """
    prompts = {}
    
    if not os.path.exists(prompts_dir):
        print_safe(f"Warning: Prompts directory {prompts_dir} does not exist")
        return prompts
    
    # Load each prompt file
    for filename in os.listdir(prompts_dir):
        if filename.endswith(".txt"):
            folder_name = os.path.splitext(filename)[0]
            file_path = os.path.join(prompts_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompt_text = f.read()
                prompts[folder_name] = prompt_text
                # 显示加载的prompt的前100个字符，方便验证
                preview = prompt_text[:100].replace("\n", " ") + "..."
                print_safe(f"Loaded prompt for folder type: {folder_name} - Preview: {preview}")
                
                # 检查是否包含title和coarse_input占位符
                if '{title}' in prompt_text and '{coarse_input}' in prompt_text:
                    print_safe(f"  - Prompt contains title and coarse_input placeholders ✓")
                else:
                    print_safe(f"  - Warning: Prompt does not contain required placeholders!")
            except Exception as e:
                print_safe(f"Error loading prompt file {filename}: {str(e)}")
    
    return prompts

def select_prompt_for_folder(folder_name: str, default_prompt: str) -> str:
    """根据文件夹名称选择适当的prompt
    
    Args:
        folder_name (str): 文件夹名称
        default_prompt (str): 默认prompt
        
    Returns:
        str: 选择的prompt
    """
    folder_name_lower = folder_name.lower()
    
    # 根据文件夹名中的关键词选择prompt
    if "random_folding" in folder_name_lower:
        if "random_folding" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'random_folding' 提示词")
            return PROMPTS["random_folding"]
    elif "folding" in folder_name_lower:
        if "folding" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'folding' 提示词")
            return PROMPTS["folding"]
    elif "picking" in folder_name_lower:
        if "picking" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'picking' 提示词")
            return PROMPTS["picking"]
    
    # 精确匹配
    if folder_name in PROMPTS:
        print_safe(f"文件夹 '{folder_name}' 精确匹配到相应提示词")
        return PROMPTS[folder_name]
    
    # 无匹配，使用默认prompt
    print_safe(f"文件夹 '{folder_name}' 未匹配到任何提示词，使用默认提示词")
    return default_prompt

class Strategy(Enum):
    CONSTANT_INTERVAL = "constant_interval"
    EVEN_INTERVAL = "even_interval"

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 计算视频时长(秒)
    duration = length / fps
    
    # 如果是EVEN_INTERVAL策略，根据视频时长动态调整max_frames
    if extraction_strategy == Strategy.EVEN_INTERVAL:
        # 视频时长以5为单位向上取整
        adjusted_max_frames = int(math.ceil(duration / 10) * 10)
        #print_safe(f"视频时长: {duration:.2f}秒, 调整后的max_frames: {adjusted_max_frames}")
        max_frames = adjusted_max_frames
        frame_interval = int(length / max_frames)
    elif extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    else:
        raise ValueError("Invalid extraction strategy")
    
    frame_count = 0
    keyframes = []
    timestamps = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(image_path, frame)
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    #print_safe(f"sampled frames: {len(keyframes)}")
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    system_prompt = """You are a video action annotation specialist with expertise in analyzing and labeling human actions in videos. Your task is to carefully observe the video frames and provide detailed, accurate annotations of the actions being performed.

Key responsibilities:
1. Identify and label key actions with precise timing
2. Focus on significant manipulations and state changes
3. Provide clear, concise descriptions of actions
4. Maintain consistent formatting in your output
5. Ensure temporal accuracy in your annotations

Please follow the specific instructions provided in the user prompt for each video analysis task."""
    
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "system",
            "content": system_prompt,
        },
        {
            "role": "user",
            "content": content,
        }
    ]

def get_coarse_annotations(json_path: str, video_name: str) -> tuple[str, str]:
    """从JSON文件中读取指定video_name的raw_output字段，并分离title和coarse input"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        
        # 移除文件扩展名以匹配键名
        video_name_without_ext = os.path.splitext(video_name)[0]
        
        if not data or video_name_without_ext not in data:
            raise ValueError(f"No data found for {video_name_without_ext} in JSON file")
        
        entry = data[video_name_without_ext]
        title = entry.get('title', '')
        raw_output = entry.get('raw_output', '')
        
        # 如果raw_output为空，尝试从actions构建
        if not raw_output and 'actions' in entry:
            actions = entry['actions']
            raw_output = title + '\n'
            for action in actions:
                # 将帧数转换为时间戳
                start_time = f"{action['start_frame'] // (action.get('fps', 30) * 60):02d}:{(action['start_frame'] % (action.get('fps', 30) * 60)) // action.get('fps', 30):02d}"
                end_time = f"{action['end_frame'] // (action.get('fps', 30) * 60):02d}:{(action['end_frame'] % (action.get('fps', 30) * 60)) // action.get('fps', 30):02d}"
                raw_output += f"[{start_time} - {end_time}] {action['description']}\n"
        
        return title, raw_output.strip()

def save_dense_result(log_folder, filename, result, model_name, title, output_file, fps):
    """Save the fine-grained (dense) analysis result to a JSON file.
    
    Args:
        log_folder (str): Folder to save results
        filename (str): Name of the processed file
        result (str): Raw dense result from the model
        model_name (str): Name of the model used
        title (str): Video title
        output_file (str): Output JSON file name
        fps (float): Actual frames per second of the video
        
    Returns:
        str: Path to the saved dense JSON file
    """
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)
    
    video_name = os.path.splitext(filename)[0]
    
    # 解析细粒度标注中的时间戳和描述
    dense_actions = []
    fps_int = int(round(fps))
    
    for line in result.split('\n'):
        if line.strip():
            match = re.match(r'\[(\d{2}:\d{2}) - (\d{2}:\d{2})\] (.*)', line.strip())
            if match:
                start_time, end_time, description = match.groups()
                start_min, start_sec = map(int, start_time.split(':'))
                end_min, end_sec = map(int, end_time.split(':'))
                # 使用视频的实际fps进行转换
                start_frame = start_min * 60 * fps_int + start_sec * fps_int
                end_frame = end_min * 60 * fps_int + end_sec * fps_int
                dense_actions.append({
                    "start_frame": start_frame,
                    "end_frame": end_frame,
                    "description": description.strip(),
                    "fps": fps_int
                })
    
    # 创建结果数据结构
    result_data = {
        video_name: {
            "video_name": video_name,
            "title": title,
            "actions": dense_actions,
            "raw_output": result.strip(),
            "model": model_name,
            "fps": fps_int
        }
    }
    
    # 保存结果到JSON文件
    result_file = os.path.join(log_folder, output_file)
    
    # 如果文件已存在，读取并更新
    if os.path.exists(result_file):
        try:
            with open(result_file, "r", encoding='utf-8') as f:
                existing_data = json.load(f)
            existing_data.update(result_data)
            result_data = existing_data
        except json.JSONDecodeError:
            print_safe(f"警告: 现有文件 {result_file} 不是有效的JSON，将被覆盖")
    
    with open(result_file, "w", encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print_safe(f"已保存dense结果到: {result_file}")
    return result_file

def ensure_temp_dir():
    """确保临时目录存在并清空"""
    try:
        if os.path.exists(TEMP_CONFIG['temp_base_dir']):
            # 如果目录存在，先清空它
            for root, dirs, files in os.walk(TEMP_CONFIG['temp_base_dir'], topdown=False):
                for name in files:
                    try:
                        file_path = os.path.join(root, name)
                        os.chmod(file_path, 0o777)  # 修改文件权限
                        os.remove(file_path)
                    except Exception as e:
                        logging.error(f"无法删除文件 {file_path}: {str(e)}")
                
                for name in dirs:
                    try:
                        dir_path = os.path.join(root, name)
                        os.chmod(dir_path, 0o777)  # 修改目录权限
                        os.rmdir(dir_path)
                    except Exception as e:
                        logging.error(f"无法删除目录 {dir_path}: {str(e)}")
        else:
            os.makedirs(TEMP_CONFIG['temp_base_dir'])
        
        logging.info(f"临时目录已准备: {TEMP_CONFIG['temp_base_dir']}")
    except Exception as e:
        logging.error(f"准备临时目录失败: {str(e)}")
        raise

@contextlib.contextmanager
def temp_directory(prefix="temp_", suffix=""):
    """临时目录上下文管理器，确保目录被完全清理"""
    temp_dir = None
    try:
        # 在系统临时目录中创建临时目录
        temp_dir = tempfile.mkdtemp(prefix=prefix, suffix=suffix, dir=TEMP_CONFIG['temp_base_dir'])
        logging.info(f"创建临时目录: {temp_dir}")
        yield temp_dir
    except Exception as e:
        logging.error(f"临时目录操作失败: {str(e)}")
        raise
    finally:
        if temp_dir and os.path.exists(temp_dir):
            try:
                # 确保所有文件都被关闭
                for root, dirs, files in os.walk(temp_dir, topdown=False):
                    for name in files:
                        try:
                            file_path = os.path.join(root, name)
                            os.chmod(file_path, 0o777)  # 修改文件权限
                            os.remove(file_path)
                        except Exception as e:
                            logging.error(f"无法删除文件 {file_path}: {str(e)}")
                    
                    for name in dirs:
                        try:
                            dir_path = os.path.join(root, name)
                            os.chmod(dir_path, 0o777)  # 修改目录权限
                            os.rmdir(dir_path)
                        except Exception as e:
                            logging.error(f"无法删除目录 {dir_path}: {str(e)}")
                
                # 最后删除临时目录本身
                os.chmod(temp_dir, 0o777)
                os.rmdir(temp_dir)
                if not os.path.exists(temp_dir):
                    logging.info(f"成功删除临时目录: {temp_dir}")
                else:
                    logging.warning(f"临时目录 {temp_dir} 删除失败")
            except Exception as e:
                logging.error(f"删除临时目录失败: {str(e)}")
                # 如果无法删除，记录到日志
                with open(os.path.join(os.path.dirname(TEMP_CONFIG['temp_base_dir']), "temp_cleanup_failures.txt"), "a", encoding='utf-8') as f:
                    f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Failed to remove: {temp_dir}\n")
                    f.write(f"Error: {str(e)}\n")
                    f.write("-" * 50 + "\n")

def process_single_video(video_path: str, log_folder: str, coarse_json_path: str, 
                        model_name: str, dense_output_file: str, client_idx: int,
                        folder_name: str = None, default_prompt: str = None) -> dict:
    """处理单个视频文件，生成细粒度标注并保存结果。"""
    filename = os.path.basename(video_path)
    video_name = os.path.splitext(filename)[0]
    
    try:
        # 使用临时目录上下文管理器
        with temp_directory(prefix=f"temp_{client_idx}_{video_name}_") as temp_dir:
            temp_frames_dir = os.path.join(temp_dir, "frames")
            os.makedirs(temp_frames_dir, exist_ok=True)
            
            logging.info(f"开始处理: {filename}")
            
            # 获取API客户端
            client = CLIENTS[client_idx % len(CLIENTS)]
            
            # 获取标题和粗标注
            title, coarse_input = get_coarse_annotations(coarse_json_path, filename)
            
            # 选择提示词模板
            prompt_template = select_prompt_for_folder(folder_name, default_prompt) if folder_name else default_prompt
            prompt = prompt_template.format(title=title, coarse_input=coarse_input)
            
            # 获取视频fps
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            cap.release()
            
            # 处理视频帧
            selected_images, timestamps = preprocess_video(
                video_file_path=video_path,
                output_dir=temp_frames_dir,
                extraction_strategy=Strategy.EVEN_INTERVAL,
                interval_in_seconds=VIDEO_CONFIG['sampling_interval'],
                use_timestamp=True,
                max_frames=VIDEO_CONFIG['max_frames']
            )
            
            # 构建消息并调用API
            message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
            response = client.chat.completions.create(
                model=model_name,
                messages=message
            )
            result = response.choices[0].message.content
            
            # 保存结果
            save_dense_result(
                log_folder=log_folder,
                filename=filename,
                result=result,
                model_name=model_name,
                title=title,
                output_file=dense_output_file,
                fps=fps
            )
            
            logging.info(f"完成处理: {filename}")
            return {filename: result}
            
    except Exception as e:
        logging.error(f"处理失败: {filename} - {str(e)}")
        return {}

def process_videos_concurrently(video_paths, log_folder, coarse_json_path, model_name, dense_output_file, 
                              folder_name=None, default_prompt=None):
    """并发处理多个视频文件"""
    results = {}
    total_count = len(video_paths)
    
    # 使用tqdm创建进度条
    with tqdm(total=total_count, desc="处理视频") as pbar:
        # 创建线程池
        max_workers = min(VIDEO_CONFIG['max_workers'], total_count)
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_path = {
                executor.submit(
                    process_single_video,
                    path,
                    log_folder,
                    coarse_json_path,
                    model_name,
                    dense_output_file,
                    i % len(CLIENTS),
                    folder_name,
                    default_prompt
                ): path for i, path in enumerate(video_paths)
            }
            
            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_path):
                path = future_to_path[future]
                try:
                    result = future.result()
                    if result:
                        results.update(result)
                except Exception as e:
                    logging.error(f"处理失败: {os.path.basename(path)} - {str(e)}")
                finally:
                    pbar.update(1)
                    pbar.set_postfix({
                        '成功': len(results),
                        '总数': total_count
                    })
    
    return results

def should_process_folder(folder_name: str) -> bool:
    """Check if a folder should be processed based on its name.
    
    Criteria: Folder name must contain 'folding' but not 'random'.
    
    Args:
        folder_name (str): Name of the folder to check
        
    Returns:
        bool: True if the folder should be processed, False otherwise
    """
    folder_name_lower = folder_name.lower()
    
    # Check if the folder name contains 'folding' but not 'random'
    return 'folding' in folder_name_lower and 'random' not in folder_name_lower

def process_one_folder(folder_path, video_paths, log_folder, standard_base_path, base_video_path, model_name,
                       completion_queue, default_prompt):
    """处理一个文件夹中的所有视频"""
    folder_name = os.path.basename(folder_path)
    video_count = len(video_paths)
    folder_start_time = time.time()

    try:
        # 获取相对路径
        if folder_path.startswith(base_video_path):
            relative_path = os.path.relpath(folder_path, base_video_path)
        else:
            relative_path = os.path.basename(folder_path)

        # 构建标准结果JSON文件路径
        standard_json_path = os.path.join(standard_base_path, relative_path, f"{folder_name}.json")

        # 检查标准结果文件是否存在
        if not os.path.exists(standard_json_path):
            print_safe(f"[文件夹 {folder_name}] 标准结果文件不存在: {standard_json_path}")
            completion_queue.put((folder_path, 0, video_count, False))
            return 0

        # 创建输出目录
        dense_output_dir = os.path.join(log_folder, relative_path)
        if not os.path.exists(dense_output_dir):
            os.makedirs(dense_output_dir, exist_ok=True)

        # 创建输出文件名
        dense_output_filename = f"{folder_name}.json"

        # 并发处理当前文件夹的视频
        results = process_videos_concurrently(
            video_paths=video_paths,
            log_folder=dense_output_dir,
            coarse_json_path=standard_json_path,
            model_name=model_name,
            dense_output_file=dense_output_filename,
            folder_name=folder_name,
            default_prompt=default_prompt
        )

        # 计算文件夹处理时间
        folder_time = time.time() - folder_start_time
        folder_mins, folder_secs = divmod(folder_time, 60)
        
        print_safe(f"[文件夹 {folder_name}] 处理完成 - 用时: {int(folder_mins)}分{int(folder_secs)}秒")

        # 通知主线程已完成
        completion_queue.put((folder_path, len(results), video_count, True))

        return len(results)

    except Exception as e:
        print_safe(f"[文件夹 {folder_name}] 处理失败: {str(e)}")
        completion_queue.put((folder_path, 0, video_count, False))
        return 0

def process_directory_recursively(base_path, log_folder, standard_base_path, default_prompt, base_video_path=None, model_name=VIDEO_CONFIG['model_name']):
    """递归处理目录及其所有子目录中的视频文件"""
    if not os.path.exists(base_path):
        logging.error(f"指定的路径不存在: {base_path}")
        return 0
        
    base_video_path = base_video_path or base_path
    
    # 创建失败记录文件
    failure_log_path = os.path.join(log_folder, "failure_log.txt")
    failure_records = []
    
    # 收集所有视频文件
    folder_videos = {}
    for root, _, files in os.walk(base_path):
        if "pants" not in root.lower():
            continue
            
        video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        if video_files:
            folder_videos[root] = [os.path.join(root, f) for f in video_files]
    
    if not folder_videos:
        logging.warning("没有找到任何符合条件的视频文件")
        return 0
    
    # 统计信息
    total_folders = len(folder_videos)
    total_videos = sum(len(videos) for videos in folder_videos.values())
    processed_folders = 0
    processed_videos = 0
    skipped_folders = 0
    skipped_videos = 0
    failed_videos = 0  # 新增：专门统计失败的视频数
    
    # 记录开始时间
    start_time = time.time()
    
    print_safe(f"\n开始处理 {total_folders} 个文件夹，共 {total_videos} 个视频")
    print_safe("=" * 50)
    
    # 使用tqdm创建进度条
    with tqdm(total=total_folders, desc="处理文件夹") as pbar:
        for folder_path, video_paths in folder_videos.items():
            folder_name = os.path.basename(folder_path)
            
            try:
                # 获取相对路径
                relative_path = os.path.relpath(folder_path, base_video_path)
                
                # 构建标准结果JSON文件路径
                standard_json_path = os.path.join(standard_base_path, relative_path, f"{folder_name}.json")
                
                if not os.path.exists(standard_json_path):
                    error_msg = f"标准结果文件不存在: {standard_json_path}"
                    logging.warning(error_msg)
                    skipped_folders += 1
                    skipped_videos += len(video_paths)
                    # 记录所有视频的失败信息
                    for video_path in video_paths:
                        failure_records.append({
                            'video_path': video_path,
                            'folder': folder_name,
                            'error': error_msg,
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                    continue
                
                # 创建输出目录
                dense_output_dir = os.path.join(log_folder, relative_path)
                os.makedirs(dense_output_dir, exist_ok=True)
                
                # 处理当前文件夹的视频
                results = process_videos_concurrently(
                    video_paths=video_paths,
                    log_folder=dense_output_dir,
                    coarse_json_path=standard_json_path,
                    model_name=model_name,
                    dense_output_file=f"{folder_name}.json",
                    folder_name=folder_name,
                    default_prompt=default_prompt
                )
                
                # 更新统计信息
                processed_folders += 1
                processed_videos += len(results)
                
                # 检查处理失败的视频
                processed_video_names = set(results.keys())
                for video_path in video_paths:
                    video_name = os.path.basename(video_path)
                    if video_name not in processed_video_names:
                        failed_videos += 1
                        failure_records.append({
                            'video_path': video_path,
                            'folder': folder_name,
                            'error': '处理失败或未返回结果',
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                
                # 计算进度和速度
                elapsed_time = time.time() - start_time
                progress_percent = (processed_folders / total_folders) * 100
                videos_per_min = (processed_videos / elapsed_time) * 60 if elapsed_time > 0 else 0
                
                # 更新进度信息
                print_safe(f"\n当前进度: {progress_percent:.1f}%")
                print_safe(f"已处理: {processed_folders}/{total_folders} 个文件夹")
                print_safe(f"已处理视频: {processed_videos}/{total_videos}")
                print_safe(f"处理速度: {videos_per_min:.1f} 视频/分钟")
                print_safe(f"已跳过: {skipped_folders} 个文件夹, {skipped_videos} 个视频")
                print_safe(f"失败视频数: {failed_videos}")
                print_safe("-" * 30)
                
            except Exception as e:
                error_msg = f"处理文件夹失败: {folder_name} - {str(e)}"
                logging.error(error_msg)
                skipped_folders += 1
                skipped_videos += len(video_paths)
                # 记录所有视频的失败信息
                for video_path in video_paths:
                    failed_videos += 1
                    failure_records.append({
                        'video_path': video_path,
                        'folder': folder_name,
                        'error': error_msg,
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
            finally:
                pbar.update(1)
                pbar.set_postfix({
                    '成功': processed_folders,
                    '总数': total_folders,
                    '进度': f"{progress_percent:.1f}%"
                })
    
    # 保存失败记录
    if failure_records:
        with open(failure_log_path, 'w', encoding='utf-8') as f:
            f.write("处理失败的视频记录：\n")
            f.write("=" * 50 + "\n")
            for record in failure_records:
                f.write(f"\n时间: {record['timestamp']}\n")
                f.write(f"文件夹: {record['folder']}\n")
                f.write(f"视频路径: {record['video_path']}\n")
                f.write(f"失败原因: {record['error']}\n")
                f.write("-" * 30 + "\n")
        print_safe(f"\n失败记录已保存到: {failure_log_path}")
    
    # 计算总耗时
    total_time = time.time() - start_time
    hours, remainder = divmod(total_time, 3600)
    minutes, seconds = divmod(remainder, 60)
    
    # 输出最终统计
    print_safe("\n" + "=" * 50)
    print_safe("处理完成统计:")
    print_safe(f"总文件夹数: {total_folders}")
    print_safe(f"处理文件夹数: {processed_folders}")
    print_safe(f"总视频数: {total_videos}")
    print_safe(f"处理视频数: {processed_videos}")
    print_safe(f"跳过文件夹: {skipped_folders}")
    print_safe(f"跳过视频: {skipped_videos}")
    print_safe(f"失败视频数: {failed_videos}")
    print_safe(f"总耗时: {int(hours)}小时 {int(minutes)}分钟 {int(seconds)}秒")
    print_safe(f"平均速度: {(processed_videos / total_time * 60):.1f} 视频/分钟")
    print_safe("=" * 50)
    
    return processed_folders

# 主函数
if __name__ == "__main__":
    # 确保临时目录存在
    ensure_temp_dir()
    
    # 指定结果保存文件夹（密集标注结果的基础路径）
    log_folder = r"seed_annotion\results\results_dense_t"
    
    # 标准结果JSON文件的基础路径
    standard_base_path = r"seed_annotion\results\results_standard_t"
    
    # 视频文件的基础路径
    video_base_path = r"C:\Users\<USER>\Desktop\static_aloha"
    
    # 指定保存提示词的文件夹
    prompts_folder = r"seed_annotion\prompt\dense"
    

    print_safe(f"API密钥数量: {len(API_KEYS)}")
    print_safe(f"最大并发线程数: {VIDEO_CONFIG['max_workers']}")
    print_safe(f"使用模型: {VIDEO_CONFIG['model_name']}")
    print_safe(f"提示词文件夹: {prompts_folder}")
    print_safe(f"输出结果文件夹: {log_folder}")
    print_safe(f"标准标注数据路径: {standard_base_path}")
    print_safe("=" * 50)
    
    # 加载提示词
    print_safe("开始加载提示词文件...")
    PROMPTS = load_prompts_from_directory(prompts_folder)
    
    # 如果没有加载到任何提示词，使用默认提示词
    if not PROMPTS:
        print_safe("警告: 未加载到任何提示词文件，将使用默认提示词")
        print_safe("创建默认提示词...")
        PROMPTS["default"] = """
You are an expert video action annotation specialist tasked with creating detailed, fine-grained (dense) annotations in English, based on existing coarse-grained annotations. Your goal is to break down each coarse action into a sequence of precise sub-actions that capture the key manipulations in the video.

Instructions:
Review the provided coarse-grained annotations and the video title carefully.

For each coarse annotation with its time interval, create fine-grained annotations that describe the sequence of actions occurring within that interval.

Sub-Action Granularity (General Guideline):

For short coarse actions (≤5 seconds in duration): keep original actions.
For medium coarse actions (5-10 seconds): Aim for 2-4 fine-grained sub-actions.
For long coarse actions (≥10 seconds): Aim for 4-5 fine-grained sub-actions.
This is a general guideline; the actual number of sub-actions should be driven by the distinct manipulations observed, except for "FOLD" actions which have a specific requirement (see instruction 4).

Special Handling for "FOLD" Coarse Actions:

If the coarse-grained annotation describes a "fold" action (e.g., "[00:12 - 00:18] fold the flattened black shirt..."):
    The number of *actual folding sub-actions* you identify and output MUST be between 2 and 4. These sub-actions should represent distinct, significant folding motions that contribute to the final folded state of the garment.
    Crucially, actions performed during any initial 'flattening' or 'spreading out' phase, even if they involve temporary overlapping of fabric (e.g., smoothing out a sleeve by laying it over the body of the shirt *before* the main folding process begins), are NOT considered 'folds' for this counting purpose.
    A 'fold' sub-action, for counting purposes, only begins *after* the garment has been fully spread out or flattened on a surface. The first counted fold is the first manipulation that intentionally and permanently reduces the garment's surface area after it has reached this flattened state.
    For each of these 2-4 folding sub-actions, you MUST specify the degree or extent of the fold. Use clear, quantified terms like:
        "fold the shirt in half (to 1/2)"
        "fold the left third of the shirt over the center (to 1/3)"
    Do NOT include other actions like 'grasp', 'lift', 'reposition', or 'smooth out' as separate sub-actions within this "fold" segment. These minor actions, if present, are considered integral parts of one of continuous folding motions. The focus is solely on the state change achieved by each of the 2-4 identified folds.

For each fine-grained action (including folds and other actions):

The specific action being performed (verb).
The object being manipulated with its current state/condition.
The starting location/position of the object.
The ending location/position of the object (if applicable).
Describe object states clearly (e.g., "crumpled shirt", "flattened shirt", "shirt folded to 1/2").
Specify locations precisely (e.g., "from the left side of the table", "towards the center", "into the storage bin").
Use present tense verb forms (e.g., "fold the shirt," not "folding the shirt").
Focus on object manipulation rather than just movement.
Capture all key actions including: grasping, lifting, moving, placing, folding (as per instruction 4), unfolding, flattening, spreading, transferring, and any other significant manipulations.
Be comprehensive: include every action that results in a meaningful change to the object's position, orientation, or state.
Be specific and descriptive but keep each action concise.
List each action on a separate line.
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line. These timestamps must be accurate and reflect the actual start and end of the fine-grained sub-action within the video segment defined by the coarse annotation. Ensure the sub-action timestamps are sequential and collectively cover the coarse action's duration.
Do not include any numbering, bullets, or additional formatting beyond the specified [MM:SS - MM:SS] description.
Only output the action descriptions, nothing else.

Video Title:
{title}

Coarse-grained Annotations (Input for this task):
{coarse_input}

Expected Fine-grained Output Format:
[MM:SS - MM:SS] fine-grained action 1 (e.g., grasp the crumpled shirt from the pile)
[MM:SS - MM:SS] fine-grained action 2 (e.g., move the crumpled shirt to the center of the table)
[MM:SS - MM:SS] fine-grained action 3 (e.g., flatten the crumpled shirt on the table)
[MM:SS - MM:SS] fine-grained action 4 (e.g., fold the right half of the flattened shirt over the left half to 1/2)
[MM:SS - MM:SS] fine-grained action 5 (e.g., fold the top half of the half-folded shirt down to the bottom half to 1/4)
...
"""
    
    # 设置默认提示词
    default_prompt = PROMPTS.get("default", PROMPTS[list(PROMPTS.keys())[0]] if PROMPTS else None)
    if default_prompt:
        print_safe("已设置默认提示词")
    else:
        print_safe("错误: 无法设置默认提示词，程序将退出")
        sys.exit(1)
    
    # 获取要处理的基础路径（可以是命令行参数或直接指定）
    if len(sys.argv) > 1:
        base_path = sys.argv[1]
        print_safe(f"从命令行参数获取处理路径: {base_path}")
    else:
        # 默认处理的视频文件夹
        base_path = video_base_path
        print_safe(f"使用默认处理路径: {base_path}")
    
    # 模型名称
    model_name = VIDEO_CONFIG['model_name']
    
    print_safe("=" * 50)
    print_safe(f"开始递归处理目录: {base_path}")
    print_safe(f"使用 {len(CLIENTS)} 个API并行处理")
    print_safe(f"最大并发数: {VIDEO_CONFIG['max_workers']}")
    print_safe("=" * 50)
    
    processed_folders = process_directory_recursively(
        base_path,           # 要处理的基础路径 
        log_folder,          # 结果保存文件夹
        standard_base_path,  # 标准结果JSON文件的基础路径
        default_prompt,      # 默认提示词
        video_base_path,     # 视频文件的基础路径，用于生成相对路径结构
        model_name           # 模型名称
    )
    print_safe("=" * 50)
    print_safe(f"处理完成，共处理了 {processed_folders} 个包含视频的文件夹")
    print_safe("=" * 50) 