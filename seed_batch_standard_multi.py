from typing import Optional, Dict
from enum import Enum
import os
import base64
import shutil
import json
from datetime import datetime
import math
import cv2
import numpy as np
from openai import OpenAI
import sys
import concurrent.futures
import threading
import queue
import time
import random

# Initialize OpenAI client pool
API_KEYS = [
    "3fe079be-2fa4-493e-bf9c-eec8e5315f48",
    "b69a3181-336f-4121-b10c-c94390cfdcc0",
    "90989f87-e5e2-4f33-a1ed-07b7d4064ba8",
    "11fbfe30-5c81-42ee-81f8-0c0878be0bd4",
    "872b18f5-7e18-4600-be87-03114d1224e0",
    "3fe079be-2fa4-493e-bf9c-eec8e5315f48",
    "b69a3181-336f-4121-b10c-c94390cfdcc0",
    "90989f87-e5e2-4f33-a1ed-07b7d4064ba8",
    "11fbfe30-5c81-42ee-81f8-0c0878be0bd4",
    "872b18f5-7e18-4600-be87-03114d1224e0",
    # Add more API keys as needed
]

# Video processing configuration
VIDEO_CONFIG = {
    'sampling_fps': 1,
    'max_frames': 20,
    'sampling_interval': 1.0,  # 1.0 / sampling_fps
    'model_name': "doubao-1-5-thinking-vision-pro-250428",
    'max_workers': len(API_KEYS)  # Maximum number of concurrent workers
}

# Create OpenAI client pool
CLIENTS = []
for api_key in API_KEYS:
    client = OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key
    )
    CLIENTS.append(client)
    print(f"Created OpenAI client: {api_key[:10]}...")

# Thread-safe print function
print_lock = threading.Lock()

def print_safe(message):
    with print_lock:
        print(message)

# Dictionary to store prompts
PROMPTS = {}

def load_prompts_from_directory(prompts_dir: str) -> Dict[str, str]:
    """Load prompts from text files in the specified directory.
    
    Args:
        prompts_dir (str): Path to directory containing prompt files
        
    Returns:
        Dict[str, str]: Dictionary mapping folder names to prompts
    """
    prompts = {}
    
    if not os.path.exists(prompts_dir):
        print(f"Warning: Prompts directory {prompts_dir} does not exist")
        return prompts
    
    # Load each prompt file
    for filename in os.listdir(prompts_dir):
        if filename.endswith(".txt"):
            folder_name = os.path.splitext(filename)[0]
            file_path = os.path.join(prompts_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompt_text = f.read()
                prompts[folder_name] = prompt_text
                print(f"Loaded prompt for folder type: {folder_name}")
            except Exception as e:
                print(f"Error loading prompt file {filename}: {str(e)}")
    
    return prompts

class Strategy(Enum):
    # sampling strategies
    CONSTANT_INTERVAL = "constant_interval"  # sampling at a constant interval, fps sampling
    EVEN_INTERVAL = "even_interval"  # sampling at an even interval, uniform sampling

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 计算视频时长(秒)
    duration = length / fps
    
    # 如果是EVEN_INTERVAL策略，根据视频时长动态调整max_frames
    if extraction_strategy == Strategy.EVEN_INTERVAL:
        # 视频时长以5为单位向上取整
        # adjusted_max_frames = int(math.ceil(duration / 5) * 5)
        # print(f"视频时长: {duration:.2f}秒, 调整后的max_frames: {adjusted_max_frames}")
        # max_frames = adjusted_max_frames
        frame_interval = int(length / max_frames)
    elif extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    else:
        raise ValueError("Invalid extraction strategy")
    
    frame_count = 0
    keyframes = []
    timestamps = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(image_path, frame)
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    print("sampled frames:", len(keyframes))
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    """Construct messages for the video understanding"""
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            # add timestamp for each frame
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "user",
            "content": content,
        }
    ]

def convert_to_json_format(text_output: str, fps: float = 50, video_filename: str = "") -> dict:
    """Convert model output text to JSON format with frame indices.
    
    Args:
        text_output (str): Model output text
        fps (float): Frames per second of the video
        video_filename (str): Name of the video file without extension
        
    Returns:
        dict: JSON format data with frame indices
    """
    try:
        # Split the text into lines and remove empty lines
        lines = [line.strip() for line in text_output.strip().split('\n') if line.strip()]
        
        if not lines:
            print("Error: Empty input text")
            return {}
            
        # Extract title
        title = lines[0].replace('Title: ', '').strip()
        
        # Process action lines
        actions = []
        for line in lines[1:]:
            try:
                # Check if line has proper timestamp format
                if '[' not in line or ']' not in line:
                    print(f"Warning: Line does not have proper timestamp format: {line}")
                    continue
                
                # Extract time range and action description
                time_range = line[line.find('[')+1:line.find(']')]
                action_desc = line[line.find(']')+1:].strip()
                
                # Skip if action description is empty
                if not action_desc:
                    print(f"Warning: Empty action description for time range [{time_range}]")
                    continue
                
                # Convert MM:SS format to seconds
                def time_to_seconds(time_str):
                    minutes, seconds = map(int, time_str.split(':'))
                    return minutes * 60 + seconds
                
                # Check if time range has proper format
                if ' - ' not in time_range:
                    print(f"Warning: Time range does not have proper format: {time_range}")
                    continue
                
                # Convert time to frame indices
                start_time, end_time = time_range.split(' - ')
                start_seconds = time_to_seconds(start_time)
                end_seconds = time_to_seconds(end_time)
                
                # Ensure end time is greater than start time
                if end_seconds <= start_seconds:
                    print(f"Warning: End time {end_time} is not greater than start time {start_time}")
                    continue
                
                start_frame = int(start_seconds * fps)
                end_frame = int(end_seconds * fps)
                
                actions.append({
                    'start_frame': start_frame,
                    'end_frame': end_frame,
                    'description': action_desc
                })
            except Exception as e:
                print(f"Error processing action line: {line}")
                print(f"Error details: {str(e)}")
                continue
        
        # Create the result dictionary
        result = {
            'video_name': video_filename,
            'title': title,
            'actions': actions,
            'raw_output': text_output  # Add raw model output
        }
        
        #print(f"Converted result: {json.dumps(result, indent=2)}")  # Debug print
        return result
        
    except Exception as e:
        print(f"Error converting to JSON format: {str(e)}")
        print(f"Input text: {text_output}")  # Debug print
        return {}

def check_and_adjust_action3(result_text: str) -> str:
    """Check if the placement action (last action) exceeds 2 seconds and adjust timestamps accordingly.
    
    This function is designed to handle cases where the first action (Flatten/spread) might not exist.
    It identifies actions based on their descriptions rather than fixed indices.
    
    Args:
        result_text (str): Model output text
        
    Returns:
        str: Adjusted model output text
    """
    lines = [line.strip() for line in result_text.strip().split('\n') if line.strip()]
    
    if len(lines) < 3:  # Need at least title + 2 actions (fold and place)
        return result_text
    
    title = lines[0]
    action_lines = lines[1:]  # All lines after title are action lines
    
    # Find the last two actions (should be fold and place)
    if len(action_lines) < 2:
        print("Warning: Expected at least 2 actions (fold and place), but found fewer")
        return result_text
    
    # The last action should be the placement action
    place_action = action_lines[-1]
    fold_action = action_lines[-2]
    
    # Extract time ranges
    def extract_time_range(action_line):
        if '[' not in action_line or ']' not in action_line:
            print(f"Warning: Action line does not have proper timestamp format: {action_line}")
            return None, None
        time_range = action_line[action_line.find('[')+1:action_line.find(']')]
        if ' - ' not in time_range:
            print(f"Warning: Time range does not have proper format: {time_range}")
            return None, None
        start_time, end_time = time_range.split(' - ')
        return start_time, end_time
    
    # Convert MM:SS format to seconds
    def time_to_seconds(time_str):
        if ':' not in time_str:
            print(f"Warning: Time string not in MM:SS format: {time_str}")
            return 0
        minutes, seconds = map(int, time_str.split(':'))
        return minutes * 60 + seconds
    
    # Convert seconds to MM:SS format
    def seconds_to_time(seconds):
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
    
    try:
        # Parse placement action times
        place_start, place_end = extract_time_range(place_action)
        if place_start is None or place_end is None:
            print("Warning: Could not extract time range from placement action")
            return result_text
            
        place_start_seconds = time_to_seconds(place_start)
        place_end_seconds = time_to_seconds(place_end)
        
        # Check if placement action exceeds 2 seconds
        place_duration = place_end_seconds - place_start_seconds
        
        if place_duration > 2:
            print(f"Placement action duration ({place_duration} seconds) exceeds 2 seconds. Adjusting timestamps...")
            
            # Parse fold action times
            fold_start, fold_end = extract_time_range(fold_action)
            if fold_start is None or fold_end is None:
                print("Warning: Could not extract time range from fold action")
                return result_text
                
            fold_start_seconds = time_to_seconds(fold_start)
            
            # Adjust fold action end and placement action start
            new_place_start_seconds = place_end_seconds - 2  # Make placement action exactly 2 seconds
            new_fold_end_seconds = new_place_start_seconds
            
            # Convert back to MM:SS format
            new_fold_end = seconds_to_time(new_fold_end_seconds)
            new_place_start = seconds_to_time(new_place_start_seconds)
            
            # Create new action lines
            new_fold_action = fold_action.replace(fold_end, new_fold_end)
            new_place_action = place_action.replace(place_start, new_place_start)
            
            # Replace in the result
            action_lines[-2] = new_fold_action
            action_lines[-1] = new_place_action
            
            # Reconstruct the full text
            adjusted_result = title + '\n' + '\n'.join(action_lines)
            
            print(f"Adjusted result: {adjusted_result}")
            return adjusted_result
        
        return result_text
        
    except Exception as e:
        print(f"Error adjusting placement action duration: {str(e)}")
        return result_text

def save_folder_results(log_folder, folder_path, results, base_video_path=None):
    """Save all results from a single folder to a dedicated JSON file.
    
    Args:
        log_folder (str): Base folder to save results
        folder_path (str): Full path to the folder being processed
        results (dict): Dictionary of results for all videos in the folder
        base_video_path (str): 视频文件的基础路径，用于生成相对路径结构
        
    Returns:
        str: Path to the saved JSON file
    """
    # 解析视频所在的文件夹路径结构
    # 比如 E:\midea\data\video_v2\1\fold 应该保存为 E:\midea\seed_annotion\results\1\fold\fold.json
    
    # 如果未指定base_video_path，则使用默认的视频基础路径
    if base_video_path is None:
        base_video_path = r"E:\midea\data\video_v2"  # 默认视频基础路径
    
    if folder_path.startswith(base_video_path):
        # 如果路径以base_video_path开头，获取相对路径部分
        relative_path = os.path.relpath(folder_path, base_video_path)
    else:
        # 如果不是以base_video_path开头，直接使用文件夹名作为相对路径
        relative_path = os.path.basename(folder_path)
    
    # 构建目标文件夹路径
    target_folder = os.path.join(log_folder, relative_path)
    
    # 创建目标文件夹
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)
    
    # 获取当前文件夹的上一级文件夹名称作为JSON文件名
    # 如果当前是根目录，则使用当前文件夹名
    folder_parts = os.path.normpath(relative_path).split(os.sep)
    if len(folder_parts) > 1:
        # 使用当前文件夹名（即最后一个部分）作为文件名
        json_filename = folder_parts[-1]
    else:
        # 只有一级目录，使用它作为文件名
        json_filename = folder_parts[0]
    
    # 创建结果文件路径 (使用文件夹名作为JSON文件名)
    result_file = os.path.join(target_folder, f"{json_filename}.json")
    
    # 保存结果
    with open(result_file, "w", encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"Saved folder results to {result_file}")
    return result_file

def save_result(log_folder, filename, prompt, result, model_name, folder_path, results_dict, actual_fps=30.0):
    """Save the analysis result to a results dictionary for batch saving later.
    
    Args:
        log_folder (str): Folder to save results
        filename (str): Name of the processed file
        prompt (str): Prompt used for analysis
        result (str): Raw result from the model
        model_name (str): Name of the model used
        folder_path (str): Path to the folder containing the video
        results_dict (dict): Dictionary to collect results for the current folder
        actual_fps (float): Actual FPS of the video, defaults to 30.0
        
    Returns:
        dict: Updated results dictionary
    """
    # 获取视频文件名（不带后缀）
    video_name = os.path.splitext(filename)[0]
    
    print(f"Processing result for {video_name}")
    
    # 转换结果为JSON格式
    json_result = convert_to_json_format(result, video_filename=video_name, fps=actual_fps)
    
    if not json_result:
        print(f"Warning: Empty result for {video_name}")
        return results_dict
    
    # 添加到结果字典
    results_dict[video_name] = json_result
    
    return results_dict

def process_single_video(video_path, client_idx, log_folder, prompt, folder_path, results_dict, model_name=VIDEO_CONFIG['model_name']):
    """Process a single video file and add its result to the results dictionary.
    
    Args:
        video_path (str): Path to the video file
        client_idx (int): Index of the client to use from the pool
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        folder_path (str): Path to the folder containing the video
        results_dict (dict): Dictionary to collect results for the current folder
        model_name (str): Model name to use
        
    Returns:
        dict: Updated results dictionary
    """
    if not os.path.exists(video_path):
        print_safe(f"Error: Video file does not exist: {video_path}")
        return results_dict
        
    filename = os.path.basename(video_path)
    video_name = os.path.splitext(filename)[0]
    temp_dir = None
    
    try:
        # Create unique temporary directory
        temp_dir = os.path.join(os.path.dirname(video_path),
                                f"temp_{client_idx}_{video_name}_{random.randint(1000, 9999)}")
        os.makedirs(temp_dir, exist_ok=True)
        
        # Get actual FPS
        cap = cv2.VideoCapture(video_path)
        actual_fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()
        
        # Check if FPS is valid, if not use default
        if actual_fps <= 0 or math.isnan(actual_fps):
            actual_fps = 50
            print_safe(f"Warning: Could not detect valid FPS, using default: {actual_fps}")
        # else:
        #     print_safe(f"Video FPS: {actual_fps}")
        
        # Create temporary frames directory
        temp_frames_dir = os.path.join(temp_dir, "frames")
        
        # Sample video frames
        selected_images, timestamps = preprocess_video(
            video_file_path=video_path,
            output_dir=temp_frames_dir,
            extraction_strategy=Strategy.EVEN_INTERVAL,
            interval_in_seconds=VIDEO_CONFIG['sampling_interval'],
            use_timestamp=True,
            max_frames=VIDEO_CONFIG['max_frames']
        )
        
        # Get client from pool
        client = CLIENTS[client_idx % len(CLIENTS)]
        
        # Construct messages and call API
        message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
        response = client.chat.completions.create(
            model=model_name,
            messages=message
        )
        
        # Process result
        result = response.choices[0].message.content
        
        # Convert to JSON format
        json_result = convert_to_json_format(result, video_filename=video_name, fps=actual_fps)
        
        # Add to results dictionary
        if json_result:
            results_dict[video_name] = json_result
            
        print_safe(f"[Client #{client_idx}] Processed: {filename}")
        
        return results_dict
        
    except Exception as e:
        print_safe(f"[Client #{client_idx}] Error processing {filename}: {str(e)}")
        return results_dict
        
    finally:
        # Clean up temporary files
        if temp_dir and os.path.exists(temp_dir):
            try:
                # 确保所有文件都被关闭
                for root, dirs, files in os.walk(temp_dir, topdown=False):
                    for name in files:
                        try:
                            file_path = os.path.join(root, name)
                            os.chmod(file_path, 0o777)  # 修改文件权限
                            os.remove(file_path)
                        except Exception as e:
                            print_safe(f"Warning: Could not remove file {file_path}: {str(e)}")
                    
                    for name in dirs:
                        try:
                            dir_path = os.path.join(root, name)
                            os.chmod(dir_path, 0o777)  # 修改目录权限
                            os.rmdir(dir_path)
                        except Exception as e:
                            print_safe(f"Warning: Could not remove directory {dir_path}: {str(e)}")
                
                # 最后删除临时目录本身
                os.chmod(temp_dir, 0o777)
                os.rmdir(temp_dir)
            except Exception as e:
                print_safe(f"Warning: Could not remove temporary directory {temp_dir}: {str(e)}")
                # 如果无法删除，记录到日志
                with open(os.path.join(log_folder, "temp_cleanup_failures.txt"), "a", encoding='utf-8') as f:
                    f.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - Failed to remove: {temp_dir}\n")
                    f.write(f"Error: {str(e)}\n")
                    f.write("-" * 50 + "\n")

def process_folder(folder_path, log_folder, prompt, base_video_path, progress_queue, folder_id, folder_count, failure_records, model_name=VIDEO_CONFIG['model_name']):
    """Process all videos in a folder with concurrent processing.
    
    Args:
        folder_path (str): Path to the folder containing videos
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        base_video_path (str): Base path for video files
        progress_queue (queue.Queue): Queue for tracking progress
        folder_id (int): ID of the current folder (for logging)
        folder_count (int): Total number of folders (for logging)
        failure_records (list): List to store failure records
        model_name (str): Model name to use
        
    Returns:
        int: Number of videos processed
    """
    # Check if folder exists
    if not os.path.exists(folder_path):
        return 0

    # Get all video files in folder
    video_files = [f for f in os.listdir(folder_path)
                   if os.path.isfile(os.path.join(folder_path, f)) and
                   f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]

    if not video_files:
        return 0

    print_safe(
        f"[Folder {folder_id}/{folder_count}] Processing: {os.path.basename(folder_path)} ({len(video_files)} videos)")

    # Create results dictionary for this folder
    results_dict = {}

    # Create thread pool for videos
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(CLIENTS), VIDEO_CONFIG['max_workers'])) as executor:
        futures = []
        video_paths = [os.path.join(folder_path, f) for f in video_files]
        client_indices = [i % len(CLIENTS) for i in range(len(video_files))]

        # Submit video processing tasks
        for i, video_path in enumerate(video_paths):
            future = executor.submit(
                process_single_video,
                video_path,
                client_indices[i],
                log_folder,
                prompt,
                folder_path,
                results_dict,
                model_name
            )
            futures.append((future, video_path))

        # Wait for all videos to finish
        for future, video_path in futures:
            try:
                future.result()
                progress_queue.put(("progress",))
            except Exception as e:
                error_msg = f"Video processing error: {str(e)}"
                print_safe(f"{error_msg} - {os.path.basename(video_path)}")
                progress_queue.put(("failure",))
                # 记录视频处理失败
                failure_records.append({
                    'video_path': video_path,
                    'folder': os.path.basename(folder_path),
                    'error': error_msg,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

    # Save folder results
    if results_dict:
        save_folder_results(log_folder, folder_path, results_dict, base_video_path)
        print_safe(
            f"[Folder {folder_id}/{folder_count}] Completed: {os.path.basename(folder_path)} ({len(results_dict)} videos processed)")

    return len(results_dict)

def should_process_folder(folder_name: str) -> bool:
    """Check if a folder should be processed based on its name.
    
    Criteria: Folder name must contain 'folding' but not 'random'.
    
    Args:
        folder_name (str): Name of the folder to check
        
    Returns:
        bool: True if the folder should be processed, False otherwise
    """
    folder_name_lower = folder_name.lower()
    
    # Check if the folder name contains 'folding' but not 'random'
    return 'folding' in folder_name_lower and 'random' not in folder_name_lower

def process_directory_recursively(base_path, log_folder, standard_base_path, default_prompt, base_video_path=None, model_name=VIDEO_CONFIG['model_name']):
    """Recursively process directories with folder-level parallelism.
    
    Args:
        base_path (str): Base path to process
        log_folder (str): Folder to save results
        standard_base_path (str): Base path for standard video files
        default_prompt (str): Default prompt for video analysis
        base_video_path (str): Base path for video files
        model_name (str): Model name to use
        
    Returns:
        int: Number of folders processed
    """
    # Initialize base video path
    if base_video_path is None:
        base_video_path = base_path

    # 创建失败记录文件
    failure_log_path = os.path.join(log_folder, "failure_log.txt")
    failure_records = []  # 初始化失败记录列表

    # Find all folders containing videos
    folder_paths = []
    for root, dirs, files in os.walk(base_path):
        # Skip folders that don't contain "folding"
        if "pants" not in root.lower():
            continue
            
        video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        if video_files:
            folder_paths.append(root)

    total_folders = len(folder_paths)
    if not total_folders:
        print_safe("No folders with videos found")
        return 0

    print_safe(f"Found {total_folders} folders with videos")

    # Create progress queue
    progress_queue = queue.Queue()

    # Track progress
    processed_folders = [0]
    processed_videos = [0]
    failed_videos = [0]

    # Start progress monitor thread
    def progress_monitor():
        start_time = time.time()
        while processed_folders[0] < total_folders:
            try:
                msg = progress_queue.get(timeout=30)
                if msg[0] == "progress":
                    processed_videos[0] += 1
                elif msg[0] == "failure":
                    failed_videos[0] += 1

                # Print progress every 30 seconds
                elapsed = time.time() - start_time
                if processed_videos[0] > 0:
                    videos_per_min = processed_videos[0] / elapsed * 60
                else:
                    videos_per_min = 0

                print_safe(
                    f"Folders: {processed_folders[0]}/{total_folders} | "
                    f"CompressedVideos: {processed_videos[0]} | "
                    f"Failed: {failed_videos[0]} | "
                    f"Speed: {videos_per_min:.1f} videos/min"
                )
            except queue.Empty:
                if processed_folders[0] >= total_folders:
                    break

    progress_thread = threading.Thread(target=progress_monitor)
    progress_thread.daemon = True
    progress_thread.start()

    # Create folder processing thread pool
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(total_folders, VIDEO_CONFIG['max_workers'])) as executor:
        futures = []
        folder_id = 0

        # Submit folder processing tasks
        for folder_path in folder_paths:
            folder_id += 1
            # Select appropriate prompt for this folder
            folder_name = os.path.basename(folder_path)
            prompt = select_prompt_for_folder(folder_name, default_prompt)
            
            futures.append(
                executor.submit(
                    process_folder,
                    folder_path,
                    log_folder,
                    prompt,
                    base_video_path,
                    progress_queue,
                    folder_id,
                    total_folders,
                    failure_records,
                    model_name
                )
            )

        # Collect results and update progress
        for future in concurrent.futures.as_completed(futures):
            try:
                video_count = future.result()
                processed_folders[0] += 1
            except Exception as e:
                error_msg = f"Folder processing error: {str(e)}"
                print_safe(error_msg)
                processed_folders[0] += 1
                # 记录文件夹处理失败
                failure_records.append({
                    'folder_path': folder_path,
                    'error': error_msg,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                })

    # 保存失败记录
    if failure_records:
        with open(failure_log_path, 'w', encoding='utf-8') as f:
            f.write("处理失败的记录：\n")
            f.write("=" * 50 + "\n")
            for record in failure_records:
                f.write(f"\n时间: {record['timestamp']}\n")
                f.write(f"路径: {record['folder_path']}\n")
                f.write(f"失败原因: {record['error']}\n")
                f.write("-" * 30 + "\n")
        print_safe(f"\n失败记录已保存到: {failure_log_path}")

    # Final progress update
    print_safe("\nProcessing completed")
    print_safe(f"Folders processed: {processed_folders[0]}/{total_folders}")
    print_safe(f"Total videos processed: {processed_videos[0]}")
    print_safe(f"Failed videos: {failed_videos[0]}")

    return processed_folders[0]

def select_prompt_for_folder(folder_name: str, default_prompt: str) -> str:
    """根据文件夹名称选择适当的prompt
    
    Args:
        folder_name (str): 文件夹名称
        default_prompt (str): 默认prompt
        
    Returns:
        str: 选择的prompt
    """
    folder_name_lower = folder_name.lower()
    
    # 首先处理特定任务类型
    if "random_folding" in folder_name_lower:
        # random_folding 是最具体的，所以先检查它
        if "random_folding" in PROMPTS:
            return PROMPTS["random_folding"]
    elif "folding" in folder_name_lower:
        # 然后检查 folding
        if "folding" in PROMPTS:
            return PROMPTS["folding"]
    elif "picking" in folder_name_lower:
        # 最后检查 picking
        if "picking" in PROMPTS:
            return PROMPTS["picking"]
    
    # 尝试精确匹配（如果上面的匹配失败）
    if folder_name in PROMPTS:
        return PROMPTS[folder_name]
    
    # 尝试部分匹配（保留原有逻辑作为备用）
    for key in PROMPTS:
        if key in folder_name_lower or folder_name_lower in key:
            return PROMPTS[key]
    
    # 没有匹配到，使用默认prompt
    print(f"未找到文件夹 '{folder_name}' 对应的提示词，使用默认提示词")
    return default_prompt

# 主函数
if __name__ == "__main__":
    # 指定结果保存文件夹
    log_folder = "results/results_standard_t"
    
    # 指定保存提示词的文件夹
    prompts_folder = "prompt/plan_2/standard"
    
    # 加载提示词
    PROMPTS = load_prompts_from_directory(prompts_folder)
    
    # 设置默认提示词
    default_prompt = """
You are a video action annotation specialist. Your task is to identify and label every single key action performed in the given video with detailed information about objects, their states, and locations in english.

Instructions:
Watch the video carefully and identify each and every distinct key action performed throughout the entire video - do not skip any significant manipulation or state change
For each action, include the following elements:
The specific action being performed (verb)
The object being manipulated with its current state/condition
The starting location/position of the object
The ending location/position of the object (if applicable)
Describe object states clearly (e.g., "crumpled pants", "folded pants", "flattened pants")
Specify locations precisely (e.g., "from the corner of the board", "to the center", "into the bin")
Use present tense verb forms (e.g., "fold the paper" not "folding the paper")
Focus on object manipulation rather than just movement
When a fold action is detected, describe the direction of the fold. For example: from bottom to top, from top to bottom, from left to right, from right to left. Only these four options are available.
Capture all key actions including: grasping, lifting, moving, placing, folding, unfolding, flattening, spreading, transferring, and any other significant manipulations
Be comprehensive - include every action that results in a meaningful change to the object's position, orientation, or state
Be specific and descriptive but keep each action concise
List each action on a separate line
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line, where MM:SS are the start and end timestamps (in minutes and seconds) indicating when the action starts and ends in the video (e.g., [00:12 - 00:18])
Do not include any numbering, bullets, or additional formatting
Only output the action descriptions, nothing else

Output Format:
[MM:SS - MM:SS] action 1
[MM:SS - MM:SS] action 2
[MM:SS - MM:SS] action 3
......
......
......
"""
    
    # 如果没有加载到任何提示词，使用默认提示词
    if not PROMPTS:
        print_safe("Warning: No prompt files loaded, using default prompt")
        PROMPTS["default"] = default_prompt
    
    # 获取要处理的基础路径（可以是命令行参数或直接指定）
    if len(sys.argv) > 1:
        base_path = sys.argv[1]
    else:
        # 默认处理的视频文件夹
        base_path = r"C:\Users\<USER>\Desktop\static_aloha"
    
    # 视频基础路径，用于生成相对路径结构
    base_video_path = r"C:\Users\<USER>\Desktop\static_aloha"
    
    print_safe(f"Starting recursive directory processing: {base_path}")
    print_safe(f"Using {len(CLIENTS)} API clients with {VIDEO_CONFIG['max_workers']} max workers")
    
    # Measure execution time
    start_time = time.time()
    processed_folders = process_directory_recursively(base_path, log_folder, base_video_path, default_prompt)
    elapsed_time = time.time() - start_time
    
    print_safe(f"\nProcessing completed in {elapsed_time / 60:.1f} minutes")
    print_safe(f"Processed {processed_folders} folders with videos") 