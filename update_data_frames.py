"""
check逻辑
1. 头部补齐:没有从0开始的,在最前面多加一段,annotation和下一段一致
2. 中部补齐:中间没有覆盖的(start frame和上一个endframe不相等的),多加一段,annotation和上一段一致
3. 尾部补齐:最后一个结尾和总帧数不相等的,在最后加一段,annotation和上一段一致
"""
import json
import h5py
import os
from datetime import datetime
#=44/9
def fix_annotations(actions, total_frames):
    """
    修复actions列表,保证从0开始且中间无断档,结尾和HDF5帧数一致
    """
    new_actions = []
    # 头部补齐
    if actions and actions[0]['start_frame'] != 0:
        first = actions[0]
        new_actions.append({
            'start_frame': 0,
            'end_frame': first['start_frame'],
            'description': first['description'],
            'fps': first.get('fps', 15)
        })
    # 中间补齐
    for i, action in enumerate(actions):
        if i > 0:
            prev = new_actions[-1]
            if action['start_frame'] != prev['end_frame']:
                # 插入补全段
                new_actions.append({
                    'start_frame': prev['end_frame'],
                    'end_frame': action['start_frame'],
                    'description': prev['description'],
                    'fps': prev.get('fps', 15)
                })
        new_actions.append(action)
    # 尾部补齐
    if new_actions:
        last = new_actions[-1]
        if last['end_frame'] < total_frames:
            new_actions.append({
                'start_frame': last['end_frame'],
                'end_frame': total_frames,
                'description': last['description'],
                'fps': last.get('fps', 15)
            })
    return new_actions

def process_single_json(json_path, hdf5_base_path, output_path, error_log):
    print(f"\n开始处理文件: {json_path}")
    json_dir = os.path.dirname(json_path)
    json_folder = os.path.basename(json_dir)
    parent_folder = os.path.basename(os.path.dirname(json_dir))
    hdf5_dir = os.path.join(hdf5_base_path, parent_folder, json_folder)

    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        error_msg = f"读取JSON文件失败: {json_path}\n错误信息: {str(e)}\n"
        print(error_msg)
        with open(error_log, 'a', encoding='utf-8') as f:
            f.write(error_msg + "-" * 80 + "\n")
        return

    new_data = {}
    for episode_name, episode_data in data.items():
        try:
            video_name = episode_data.get('video_name', episode_name)
            hdf5_path = os.path.join(hdf5_dir, f"{video_name}.hdf5")
            if not os.path.exists(hdf5_path):
                error_msg = f"找不到HDF5文件: {hdf5_path}\n"
                print(error_msg)
                with open(error_log, 'a', encoding='utf-8') as f:
                    f.write(error_msg + "-" * 80 + "\n")
                continue
            with h5py.File(hdf5_path, 'r') as f:
                total_frames = f['observations/images/cam_high'].shape[0]
            actions = episode_data.get('actions', [])
            fixed_actions = fix_annotations(actions, total_frames)
            episode_data['actions'] = fixed_actions
            new_data[episode_name] = episode_data
        except Exception as e:
            error_msg = f"处理episode {episode_name} 时出错:\n文件: {hdf5_path}\n错误信息: {str(e)}\n"
            print(error_msg)
            with open(error_log, 'a', encoding='utf-8') as f:
                f.write(error_msg + "-" * 80 + "\n")
            continue

    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(new_data, f, indent=2, ensure_ascii=False)
        print(f"文件处理完成: {output_path}")
    except Exception as e:
        error_msg = f"保存输出文件失败: {output_path}\n错误信息: {str(e)}\n"
        print(error_msg)
        with open(error_log, 'a', encoding='utf-8') as f:
            f.write(error_msg + "-" * 80 + "\n")

def process_all_json_files(json_base_path, hdf5_base_path, output_base_path):
    error_log = os.path.join(output_base_path, "error_log.txt")
    with open(error_log, 'w', encoding='utf-8') as f:
        f.write(f"错误日志 - 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("=" * 80 + "\n")
    json_files = []
    for root, dirs, files in os.walk(json_base_path):
        for file in files:
            if file.endswith('.json'):
                folder_name = os.path.basename(root)
                if file == f"{folder_name}.json":
                    json_files.append(os.path.join(root, file))
    print(f"找到 {len(json_files)} 个JSON文件")
    for json_path in json_files:
        rel_path = os.path.relpath(json_path, json_base_path)
        output_path = os.path.join(output_base_path, rel_path)
        try:
            process_single_json(json_path, hdf5_base_path, output_path, error_log)
        except Exception as e:
            error_msg = f"处理文件 {json_path} 时出错: {str(e)}\n"
            print(error_msg)
            with open(error_log, 'a', encoding='utf-8') as f:
                f.write(error_msg + "-" * 80 + "\n")

if __name__ == "__main__":
    json_base_path = r"E:\cqh\code\history\post_process\test_check_pants_dense"  # JSON文件的基础路径
    hdf5_base_path = r"E:\cqh\code\history\post_process\data_compressed"  # HDF5文件的基础路径
    output_base_path = r"E:\cqh\code\history\post_process\pants_dense_fixed_copy"  # 输出文件的基础路径
    os.makedirs(output_base_path, exist_ok=True)
    process_all_json_files(json_base_path, hdf5_base_path, output_base_path)