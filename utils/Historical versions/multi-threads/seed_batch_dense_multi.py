from typing import Optional, Dict
from enum import Enum
import os
import base64
import shutil
import json
from datetime import datetime
import re
import queue
import concurrent.futures
import random
import threading
import time
import math

import cv2
import numpy as np
from openai import OpenAI
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 添加多API密钥支持
API_KEYS = [
    "3fe079be-2fa4-493e-bf9c-eec8e5315f48",
    "7fd0a553-4ac6-4d95-b89f-b3871ea8fe82"
    # 在此添加其他API密钥
]

# Video processing configuration
VIDEO_CONFIG = {
    'sampling_fps': 1,
    'max_frames': 30,
    'sampling_interval': 1.0,  # 1.0 / sampling_fps
    'model_name': "doubao-1-5-thinking-vision-pro-250428",
    'max_workers': min(len(API_KEYS), 10)  # 最大并发处理数
}

# 创建OpenAI客户端池
CLIENTS = []
for api_key in API_KEYS:
    client = OpenAI(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=api_key
    )
    CLIENTS.append(client)
    print(f"已创建OpenAI客户端: {api_key[:10]}...")

# 线程安全的打印函数
print_lock = threading.Lock()

# Dictionary to store prompts
PROMPTS = {}

def print_safe(message):
    with print_lock:
        print(message)

def load_prompts_from_directory(prompts_dir: str) -> Dict[str, str]:
    """Load prompts from text files in the specified directory.
    
    Args:
        prompts_dir (str): Path to directory containing prompt files
        
    Returns:
        Dict[str, str]: Dictionary mapping folder names to prompts
    """
    prompts = {}
    
    if not os.path.exists(prompts_dir):
        print_safe(f"Warning: Prompts directory {prompts_dir} does not exist")
        return prompts
    
    # Load each prompt file
    for filename in os.listdir(prompts_dir):
        if filename.endswith(".txt"):
            folder_name = os.path.splitext(filename)[0]
            file_path = os.path.join(prompts_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompt_text = f.read()
                prompts[folder_name] = prompt_text
                # 显示加载的prompt的前100个字符，方便验证
                preview = prompt_text[:100].replace("\n", " ") + "..."
                print_safe(f"Loaded prompt for folder type: {folder_name} - Preview: {preview}")
                
                # 检查是否包含title和coarse_input占位符
                if '{title}' in prompt_text and '{coarse_input}' in prompt_text:
                    print_safe(f"  - Prompt contains title and coarse_input placeholders ✓")
                else:
                    print_safe(f"  - Warning: Prompt does not contain required placeholders!")
            except Exception as e:
                print_safe(f"Error loading prompt file {filename}: {str(e)}")
    
    return prompts

def select_prompt_for_folder(folder_name: str, default_prompt: str) -> str:
    """根据文件夹名称选择适当的prompt
    
    Args:
        folder_name (str): 文件夹名称
        default_prompt (str): 默认prompt
        
    Returns:
        str: 选择的prompt
    """
    folder_name_lower = folder_name.lower()
    
    # 根据文件夹名中的关键词选择prompt
    if "random_folding" in folder_name_lower:
        if "random_folding" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'random_folding' 提示词")
            return PROMPTS["random_folding"]
    elif "folding" in folder_name_lower:
        if "folding" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'folding' 提示词")
            return PROMPTS["folding"]
    elif "picking" in folder_name_lower:
        if "picking" in PROMPTS:
            print_safe(f"文件夹 '{folder_name}' 匹配到 'picking' 提示词")
            return PROMPTS["picking"]
    
    # 精确匹配
    if folder_name in PROMPTS:
        print_safe(f"文件夹 '{folder_name}' 精确匹配到相应提示词")
        return PROMPTS[folder_name]
    
    # 无匹配，使用默认prompt
    print_safe(f"文件夹 '{folder_name}' 未匹配到任何提示词，使用默认提示词")
    return default_prompt

class Strategy(Enum):
    CONSTANT_INTERVAL = "constant_interval"
    EVEN_INTERVAL = "even_interval"

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 计算视频时长(秒)
    duration = length / fps
    
    # 如果是EVEN_INTERVAL策略，根据视频时长动态调整max_frames
    if extraction_strategy == Strategy.EVEN_INTERVAL:
        # 视频时长以5为单位向上取整
        adjusted_max_frames = int(math.ceil(duration / 10) * 10)
        print_safe(f"视频时长: {duration:.2f}秒, 调整后的max_frames: {adjusted_max_frames}")
        max_frames = adjusted_max_frames
        frame_interval = int(length / max_frames)
    elif extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    else:
        raise ValueError("Invalid extraction strategy")
    
    frame_count = 0
    keyframes = []
    timestamps = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(image_path, frame)
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    print_safe(f"sampled frames: {len(keyframes)}")
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    system_prompt = """You are a video action annotation specialist with expertise in analyzing and labeling human actions in videos. Your task is to carefully observe the video frames and provide detailed, accurate annotations of the actions being performed.

Key responsibilities:
1. Identify and label key actions with precise timing
2. Focus on significant manipulations and state changes
3. Provide clear, concise descriptions of actions
4. Maintain consistent formatting in your output
5. Ensure temporal accuracy in your annotations

Please follow the specific instructions provided in the user prompt for each video analysis task."""
    
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "system",
            "content": system_prompt,
        },
        {
            "role": "user",
            "content": content,
        }
    ]

def get_coarse_annotations(json_path: str, video_name: str) -> tuple[str, str]:
    """从JSON文件中读取指定video_name的raw_output字段，并分离title和coarse input"""
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
        
        # 移除文件扩展名以匹配键名
        video_name_without_ext = os.path.splitext(video_name)[0]
        
        if not data or video_name_without_ext not in data:
            raise ValueError(f"No data found for {video_name_without_ext} in JSON file")
        
        entry = data[video_name_without_ext]
        title = entry.get('title', '')
        raw_output = entry.get('raw_output', '')
        
        # 如果raw_output为空，尝试从actions构建
        if not raw_output and 'actions' in entry:
            actions = entry['actions']
            raw_output = title + '\n'
            for action in actions:
                # 将帧数转换为时间戳
                start_time = f"{action['start_frame'] // (action.get('fps', 30) * 60):02d}:{(action['start_frame'] % (action.get('fps', 30) * 60)) // action.get('fps', 30):02d}"
                end_time = f"{action['end_frame'] // (action.get('fps', 30) * 60):02d}:{(action['end_frame'] % (action.get('fps', 30) * 60)) // action.get('fps', 30):02d}"
                raw_output += f"[{start_time} - {end_time}] {action['description']}\n"
        
        return title, raw_output.strip()

def save_dense_result(log_folder, filename, result, model_name, title, output_file, fps):
    """Save the fine-grained (dense) analysis result to a JSON file.
    
    Args:
        log_folder (str): Folder to save results
        filename (str): Name of the processed file
        result (str): Raw dense result from the model
        model_name (str): Name of the model used
        title (str): Video title
        output_file (str): Output JSON file name
        fps (float): Actual frames per second of the video
        
    Returns:
        str: Path to the saved dense JSON file
    """
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)
    
    video_name = os.path.splitext(filename)[0]
    
    # 解析细粒度标注中的时间戳和描述
    dense_actions = []
    fps_int = int(round(fps))
    
    for line in result.split('\n'):
        if line.strip():
            match = re.match(r'\[(\d{2}:\d{2}) - (\d{2}:\d{2})\] (.*)', line.strip())
            if match:
                start_time, end_time, description = match.groups()
                start_min, start_sec = map(int, start_time.split(':'))
                end_min, end_sec = map(int, end_time.split(':'))
                # 使用视频的实际fps进行转换
                start_frame = start_min * 60 * fps_int + start_sec * fps_int
                end_frame = end_min * 60 * fps_int + end_sec * fps_int
                dense_actions.append({
                    "start_frame": start_frame,
                    "end_frame": end_frame,
                    "description": description.strip(),
                    "fps": fps_int
                })
    
    # 创建结果数据结构
    result_data = {
        video_name: {
            "video_name": video_name,
            "title": title,
            "actions": dense_actions,
            "raw_output": result.strip(),
            "model": model_name,
            "fps": fps_int
        }
    }
    
    # 保存结果到JSON文件
    result_file = os.path.join(log_folder, output_file)
    
    # 如果文件已存在，读取并更新
    if os.path.exists(result_file):
        try:
            with open(result_file, "r", encoding='utf-8') as f:
                existing_data = json.load(f)
            existing_data.update(result_data)
            result_data = existing_data
        except json.JSONDecodeError:
            print_safe(f"警告: 现有文件 {result_file} 不是有效的JSON，将被覆盖")
    
    with open(result_file, "w", encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print_safe(f"已保存dense结果到: {result_file}")
    return result_file

def process_single_video(video_path: str, log_folder: str, coarse_json_path: str, 
                         model_name: str, dense_output_file: str, client_idx: int,
                         folder_name: str = None, default_prompt: str = None) -> dict:
    """处理单个视频文件，生成细粒度标注并保存结果。
    
    Args:
        video_path (str): 视频文件路径
        log_folder (str): 结果保存文件夹
        coarse_json_path (str): 粗粒度标注JSON文件路径
        model_name (str): 模型名称
        dense_output_file (str): 密集标注输出文件名
        client_idx (int): 客户端索引
        folder_name (str): 视频所在文件夹名称，用于选择适当的prompt
        default_prompt (str): 默认prompt
        
    Returns:
        dict: 处理结果，键为视频文件名，值为标注结果
    """
    filename = os.path.basename(video_path)
    video_name = os.path.splitext(filename)[0]
    
    # 创建唯一的临时目录名，包含客户端ID、视频名称和随机数
    rand_num = random.randint(1000, 9999)
    temp_dir = os.path.join(log_folder, f"temp_{client_idx}_{video_name}_{rand_num}")
    
    try:
        # 确保临时目录存在
        os.makedirs(temp_dir, exist_ok=True)
        print_safe(f"[客户端 #{client_idx}] 处理视频文件: {filename}")
        
        # 获取API客户端
        client = CLIENTS[client_idx % len(CLIENTS)]
        
        title, coarse_input = get_coarse_annotations(coarse_json_path, filename)
        
        # 选择适合该文件夹的prompt模板
        if folder_name and default_prompt:
            prompt_template = select_prompt_for_folder(folder_name, default_prompt)
        else:
            prompt_template = default_prompt if default_prompt else """
You are an expert video action annotation specialist tasked with creating detailed, fine-grained (dense) annotations in English, based on existing coarse-grained annotations. Your goal is to break down each coarse action into a sequence of precise sub-actions that capture the key manipulations in the video.

Instructions:
Review the provided coarse-grained annotations and the video title carefully.

For each coarse annotation with its time interval, create fine-grained annotations that describe the sequence of actions occurring within that interval.

Sub-Action Granularity (General Guideline):

For short coarse actions (≤5 seconds in duration): keep original actions.
For medium coarse actions (5-10 seconds): Aim for 2-4 fine-grained sub-actions.
For long coarse actions (≥10 seconds): Aim for 4-5 fine-grained sub-actions.
This is a general guideline; the actual number of sub-actions should be driven by the distinct manipulations observed, except for "FOLD" actions which have a specific requirement (see instruction 4).

Special Handling for "FOLD" Coarse Actions:

If the coarse-grained annotation describes a "fold" action (e.g., "[00:12 - 00:18] fold the flattened black shirt..."):
    The number of *actual folding sub-actions* you identify and output MUST be between 2 and 4. These sub-actions should represent distinct, significant folding motions that contribute to the final folded state of the garment.
    Crucially, actions performed during any initial 'flattening' or 'spreading out' phase, even if they involve temporary overlapping of fabric (e.g., smoothing out a sleeve by laying it over the body of the shirt *before* the main folding process begins), are NOT considered 'folds' for this counting purpose.
    A 'fold' sub-action, for counting purposes, only begins *after* the garment has been fully spread out or flattened on a surface. The first counted fold is the first manipulation that intentionally and permanently reduces the garment's surface area after it has reached this flattened state.
    For each of these 2-4 folding sub-actions, you MUST specify the degree or extent of the fold. Use clear, quantified terms like:
        "fold the shirt in half (to 1/2)"
        "fold the left third of the shirt over the center (to 1/3)"
    Do NOT include other actions like 'grasp', 'lift', 'reposition', or 'smooth out' as separate sub-actions within this "fold" segment. These minor actions, if present, are considered integral parts of one of continuous folding motions. The focus is solely on the state change achieved by each of the 2-4 identified folds.

For each fine-grained action (including folds and other actions):

The specific action being performed (verb).
The object being manipulated with its current state/condition.
The starting location/position of the object.
The ending location/position of the object (if applicable).
Describe object states clearly (e.g., "crumpled shirt", "flattened shirt", "shirt folded to 1/2").
Specify locations precisely (e.g., "from the left side of the table", "towards the center", "into the storage bin").
Use present tense verb forms (e.g., "fold the shirt," not "folding the shirt").
Focus on object manipulation rather than just movement.
Capture all key actions including: grasping, lifting, moving, placing, folding (as per instruction 4), unfolding, flattening, spreading, transferring, and any other significant manipulations.
Be comprehensive: include every action that results in a meaningful change to the object's position, orientation, or state.
Be specific and descriptive but keep each action concise.
List each action on a separate line.
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line. These timestamps must be accurate and reflect the actual start and end of the fine-grained sub-action within the video segment defined by the coarse annotation. Ensure the sub-action timestamps are sequential and collectively cover the coarse action's duration.
Do not include any numbering, bullets, or additional formatting beyond the specified [MM:SS - MM:SS] description.
Only output the action descriptions, nothing else.
"""
        
        # 检查prompt模板中是否已经包含{title}和{coarse_input}占位符
        if '{title}' in prompt_template and '{coarse_input}' in prompt_template:
            # 直接使用prompt模板中的占位符
            prompt = prompt_template.format(title=title, coarse_input=coarse_input)
        else:
            # 如果没有占位符，则以旧的方式拼接
            prompt = prompt_template + f"""

Video Title:
{title}

Coarse-grained Annotations (Input for this task):
{coarse_input}

Expected Fine-grained Output Format:
[MM:SS - MM:SS] fine-grained action 1 (e.g., grasp the crumpled shirt from the pile)
[MM:SS - MM:SS] fine-grained action 2 (e.g., move the crumpled shirt to the center of the table)
[MM:SS - MM:SS] fine-grained action 3 (e.g., flatten the crumpled shirt on the table)
[MM:SS - MM:SS] fine-grained action 4 (e.g., fold the right half of the flattened shirt over the left half to 1/2)
[MM:SS - MM:SS] fine-grained action 5 (e.g., fold the top half of the half-folded shirt down to the bottom half to 1/4)
...
"""
        
        # 创建帧文件夹
        temp_frames_dir = os.path.join(temp_dir, "frames")
        os.makedirs(temp_frames_dir, exist_ok=True)

        # 获取实际fps
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()
        
        # 处理视频帧
        selected_images, timestamps = preprocess_video(
            video_file_path=video_path,
            output_dir=temp_frames_dir,
            extraction_strategy=Strategy.EVEN_INTERVAL,
            interval_in_seconds=VIDEO_CONFIG['sampling_interval'],
            use_timestamp=True,
            max_frames=VIDEO_CONFIG['max_frames']
        )
        
        # 构建消息
        message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
        
        # 调用API
        response = client.chat.completions.create(
            model=model_name,
            messages=message
        )
        
        # 获取结果
        result = response.choices[0].message.content
        
        # 保存结果
        dense_file = save_dense_result(
            log_folder=log_folder,
            filename=filename,
            result=result,
            model_name=model_name,
            title=title,
            output_file=dense_output_file,
            fps=fps
        )
        
        print_safe(f"[客户端 #{client_idx}] 成功处理视频: {filename}")
        
        # 返回结果
        return {filename: result}
        
    except Exception as e:
        print_safe(f"[客户端 #{client_idx}] 处理视频 {filename} 时发生错误: {str(e)}")
        return {}
        
    finally:
        # 确保临时文件夹被清理
        try:
            if os.path.exists(temp_frames_dir):
                shutil.rmtree(temp_frames_dir)
                print_safe(f"[客户端 #{client_idx}] 已清理帧目录: {temp_frames_dir}")
        except Exception:
            pass
            
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                print_safe(f"[客户端 #{client_idx}] 已清理临时目录: {temp_dir}")
        except Exception:
            pass

def process_videos_concurrently(video_paths, log_folder, coarse_json_path, model_name, dense_output_file, 
                              folder_name=None, default_prompt=None):
    """并发处理多个视频文件，添加精确的时间计算和进度条"""
    results = {}
    total_count = len(video_paths)
    processed = 0
    start_time = time.time()
    last_update_time = time.time()

    print_safe(f"准备处理 {total_count} 个视频...")

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=len(CLIENTS)) as executor:
        # 分配客户端ID - 循环分配
        client_indices = [i % len(CLIENTS) for i in range(total_count)]

        # 提交任务
        futures = {
            executor.submit(
                process_single_video,
                path,
                log_folder,
                coarse_json_path,
                model_name,
                dense_output_file,
                client_idx,
                folder_name,
                default_prompt
            ): (path, client_idx) for (path, client_idx) in zip(video_paths, client_indices)
        }

        # 处理结果
        for future in concurrent.futures.as_completed(futures):
            path, client_idx = futures[future]
            processed += 1

            # 获取并记录结果
            try:
                result = future.result()
                if result:
                    results.update(result)
            except Exception as e:
                print_safe(f"处理视频 {path} 时发生未捕获的错误: {str(e)}")

            # 计算统计信息
            elapsed = time.time() - start_time

            # 计算视频处理速度（每分钟处理视频数）
            if elapsed > 0 and processed > 0:
                videos_per_min = (processed / elapsed) * 60
                eta = (elapsed / processed) * (total_count - processed)
            else:
                videos_per_min = 0
                eta = 0

            # 进度百分比
            progress_percent = (processed / total_count) * 100

            # 创建进度条
            bar_length = 20
            filled_len = int(bar_length * processed / total_count)
            progress_bar = f"[{'■' * filled_len}{'-' * (bar_length - filled_len)}]"

            # 格式化时间显示
            elapsed_mins, elapsed_secs = divmod(elapsed, 60)
            eta_mins, eta_secs = divmod(eta, 60)

            # 显示进度（每10秒或每处理10%时显示一次）
            current_time = time.time()
            if current_time - last_update_time > 10 or processed == total_count:
                print_safe(
                    f"进度: {progress_bar} {progress_percent:.1f}% "
                    f"({processed}/{total_count}) | "
                    f"速度: {videos_per_min:.1f} 视频/分钟 | "
                    f"已用: {int(elapsed_mins)}分{int(elapsed_secs)}秒 | "
                    f"预计剩余: {int(eta_mins)}分{int(eta_secs)}秒"
                )
                last_update_time = current_time

    # 所有视频处理完成
    total_time = time.time() - start_time
    total_mins, total_secs = divmod(total_time, 60)
    print_safe(f"\n[完成] 所有视频处理完毕!")
    print_safe(f"共处理 {len(results)}/{total_count} 个视频")
    print_safe(f"总用时: {int(total_mins)}分{int(total_secs)}秒")

    if total_time > 0 and total_count > 0:
        avg_videos_per_min = (total_count / total_time) * 60
        print_safe(f"平均速度: {avg_videos_per_min:.1f} 视频/分钟")

    return results

def should_process_folder(folder_name: str) -> bool:
    """Check if a folder should be processed based on its name.
    
    Criteria: Folder name must contain 'folding' but not 'random'.
    
    Args:
        folder_name (str): Name of the folder to check
        
    Returns:
        bool: True if the folder should be processed, False otherwise
    """
    folder_name_lower = folder_name.lower()
    
    # Check if the folder name contains 'folding' but not 'random'
    return 'folding' in folder_name_lower and 'random' not in folder_name_lower

def process_one_folder(folder_path, video_paths, log_folder, standard_base_path, base_video_path, model_name,
                       completion_queue, default_prompt):
    """处理一个文件夹中的所有视频"""
    folder_name = os.path.basename(folder_path)
    video_count = len(video_paths)
    folder_start_time = time.time()

    try:
        # 获取相对路径
        if folder_path.startswith(base_video_path):
            relative_path = os.path.relpath(folder_path, base_video_path)
        else:
            relative_path = os.path.basename(folder_path)

        # 构建标准结果JSON文件路径
        standard_json_path = os.path.join(standard_base_path, relative_path, f"{folder_name}.json")

        # 检查标准结果文件是否存在
        if not os.path.exists(standard_json_path):
            print_safe(f"[文件夹 {folder_name}] 标准结果文件不存在: {standard_json_path}")
            completion_queue.put((folder_path, 0, video_count, False))
            return 0

        # 创建输出目录
        dense_output_dir = os.path.join(log_folder, relative_path)
        if not os.path.exists(dense_output_dir):
            os.makedirs(dense_output_dir, exist_ok=True)

        # 创建输出文件名
        dense_output_filename = f"{folder_name}.json"

        # 并发处理当前文件夹的视频
        results = process_videos_concurrently(
            video_paths=video_paths,
            log_folder=dense_output_dir,
            coarse_json_path=standard_json_path,
            model_name=model_name,
            dense_output_file=dense_output_filename,
            folder_name=folder_name,
            default_prompt=default_prompt
        )

        # 计算文件夹处理时间
        folder_time = time.time() - folder_start_time
        folder_mins, folder_secs = divmod(folder_time, 60)
        
        print_safe(f"[文件夹 {folder_name}] 处理完成 - 用时: {int(folder_mins)}分{int(folder_secs)}秒")

        # 通知主线程已完成
        completion_queue.put((folder_path, len(results), video_count, True))

        return len(results)

    except Exception as e:
        print_safe(f"[文件夹 {folder_name}] 处理失败: {str(e)}")
        completion_queue.put((folder_path, 0, video_count, False))
        return 0

def process_directory_recursively(base_path, log_folder, standard_base_path, default_prompt, base_video_path=None, model_name=VIDEO_CONFIG['model_name']):
    """递归处理目录及其所有子目录中的视频文件，添加详细的进度统计"""
    start_time = time.time()
    print_safe(f"处理路径: {base_path}")

    # 如果未指定base_video_path，则使用base_path
    if base_video_path is None:
        base_video_path = base_path

    # 检查基础路径是否存在
    if not os.path.exists(base_path):
        print_safe(f"[错误] 指定的路径不存在: {base_path}")
        return 0

    # 收集所有视频文件
    print_safe("[扫描] 正在扫描目录中的视频文件...")
    folder_videos = {}  # {文件夹路径: [视频文件路径列表]}

    for root, dirs, files in os.walk(base_path):
        folder_name = os.path.basename(root)
        # if not should_process_folder(folder_name):
        #     continue  # 跳过不符合条件的文件夹
            
        video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        if video_files:
            folder_videos[root] = [os.path.join(root, f) for f in video_files]

    if not folder_videos:
        print_safe("[警告] 没有找到任何符合条件的视频文件")
        return 0

    # 统计视频总数
    total_folders = len(folder_videos)
    total_videos = sum(len(videos) for videos in folder_videos.values())
    print_safe(f"[统计] 发现 {total_folders} 个文件夹, 共 {total_videos} 个视频")

    # 初始化变量
    processed_folders = 0
    processed_videos = 0
    skipped_folders = 0
    skipped_videos = 0

    # 创建任务完成队列
    folder_completion_queue = queue.Queue()

    # 单文件视频处理
    if os.path.isfile(base_path) and base_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
        folder_path = os.path.dirname(base_path)
        folder_name = os.path.basename(folder_path)
        
        # 获取相对路径
        if folder_path.startswith(base_video_path):
            relative_path = os.path.relpath(folder_path, base_video_path)
        else:
            relative_path = os.path.basename(folder_path)
        
        # 构建标准结果JSON文件路径
        standard_json_path = os.path.join(standard_base_path, relative_path, f"{folder_name}.json")
        
        if not os.path.exists(standard_json_path):
            print_safe(f"错误: 标准结果JSON文件不存在: {standard_json_path}")
            return 0
        
        # 构建dense结果保存路径
        dense_output_dir = os.path.join(log_folder, relative_path)
        if not os.path.exists(dense_output_dir):
            os.makedirs(dense_output_dir)
        
        dense_output_file = f"{folder_name}.json"
        
        # 分配客户端ID
        client_idx = 0
        
        result = process_single_video(
            video_path=base_path, 
            log_folder=dense_output_dir, 
            coarse_json_path=standard_json_path, 
            model_name=model_name, 
            dense_output_file=dense_output_file,
            client_idx=client_idx,
            folder_name=folder_name,
            default_prompt=default_prompt
        )
        if result:
            return 1
        return 0

    # 创建外层的线程池用于处理文件夹
    folder_executor = concurrent.futures.ThreadPoolExecutor(max_workers=VIDEO_CONFIG['max_workers'])
    folder_futures = {}

    # 添加文件夹处理任务
    for folder_path, video_paths in folder_videos.items():
        folder_name = os.path.basename(folder_path)
        future = folder_executor.submit(
            process_one_folder,
            folder_path,
            video_paths,
            log_folder,
            standard_base_path,
            base_video_path,
            model_name,
            folder_completion_queue,
            default_prompt
        )
        folder_futures[future] = (folder_path, len(video_paths))
        print_safe(f"[调度] 添加文件夹任务: {folder_name} ({len(video_paths)} 个视频)")

    # 监视进度
    completed_folders = 0
    
    # 处理文件夹完成情况
    while completed_folders < total_folders:
        try:
            # 获取完成信息，设置超时避免阻塞
            folder_result = folder_completion_queue.get(timeout=10)
            folder_path, videos_processed, videos_total, success = folder_result

            # 更新统计
            processed_folders += 1
            processed_videos += videos_processed
            skipped_in_folder = videos_total - videos_processed
            skipped_videos += skipped_in_folder

            # 计算全局进度
            global_time = time.time() - start_time
            progress_percent = (processed_folders / total_folders) * 100

            # 显示完成信息
            print_safe(f"\n[完成] 文件夹 {os.path.basename(folder_path)} 处理完成: ")
            print_safe(f"  处理视频: {videos_processed}/{videos_total}")
            if not success:
                print_safe(f"  警告: 文件夹处理存在错误")

            completed_folders += 1

            # 全局进度更新
            if global_time > 0 and processed_videos > 0:
                folders_per_min = (processed_folders / global_time) * 60
                remaining_folders = total_folders - processed_folders
                eta = (global_time / processed_folders) * remaining_folders
            else:
                folders_per_min = 0
                eta = 0

            # 格式化时间
            eta_mins, eta_secs = divmod(eta, 60)
            elapsed_mins, elapsed_secs = divmod(global_time, 60)

            # 进度条
            bar_length = 30
            filled_len = int(bar_length * processed_folders / total_folders)
            progress_bar = f"[{'■' * filled_len}{'-' * (bar_length - filled_len)}]"

            # 显示全局进度
            print_safe(
                f"\n[全局进度] {progress_bar} {progress_percent:.1f}% "
                f"({processed_folders}/{total_folders} 文件夹) | "
                f"速度: {folders_per_min:.1f} 文件夹/分钟 | "
                f"已用: {int(elapsed_mins)}分{int(elapsed_secs)}秒 | "
                f"预计剩余: {int(eta_mins)}分{int(eta_secs)}秒"
            )

        except queue.Empty:
            # 没有新消息，继续等待
            pass

    # 任务完成
    folder_executor.shutdown()
    total_time = time.time() - start_time
    total_mins, total_secs = divmod(total_time, 60)

    print_safe("\n[任务完成] 所有文件夹处理完毕!")
    print_safe(f"总用时: {int(total_mins)}分{int(total_secs)}秒")
    print_safe("===============================")
    print_safe(f"处理文件夹: {processed_folders}/{total_folders}")
    print_safe(f"处理视频: {processed_videos}/{total_videos} (成功率: {processed_videos / total_videos * 100:.1f}%)")
    print_safe(f"跳过文件夹: {skipped_folders}")
    print_safe(f"跳过视频: {skipped_videos}")

    return processed_folders

# 主函数
if __name__ == "__main__":
    # 指定结果保存文件夹（密集标注结果的基础路径）
    log_folder = r"E:\midea\seed_annotion\results_dense"
    
    # 标准结果JSON文件的基础路径
    standard_base_path = r"E:\midea\seed_annotion\results_standard_multi"
    
    # 视频文件的基础路径
    video_base_path = r"E:\midea\data\video_v2\1"
    
    # 指定保存提示词的文件夹
    prompts_folder = r"E:\midea\seed_annotion\prompt\dense"
    
    print_safe("=" * 50)
    print_safe("启动多线程视频标注程序")
    print_safe("=" * 50)
    print_safe(f"API密钥数量: {len(API_KEYS)}")
    print_safe(f"最大并发线程数: {VIDEO_CONFIG['max_workers']}")
    print_safe(f"使用模型: {VIDEO_CONFIG['model_name']}")
    print_safe(f"提示词文件夹: {prompts_folder}")
    print_safe(f"输出结果文件夹: {log_folder}")
    print_safe(f"标准标注数据路径: {standard_base_path}")
    print_safe("=" * 50)
    
    # 加载提示词
    print_safe("开始加载提示词文件...")
    PROMPTS = load_prompts_from_directory(prompts_folder)
    
    # 如果没有加载到任何提示词，使用默认提示词
    if not PROMPTS:
        print_safe("警告: 未加载到任何提示词文件，将使用默认提示词")
        print_safe("创建默认提示词...")
        PROMPTS["default"] = """
You are an expert video action annotation specialist tasked with creating detailed, fine-grained (dense) annotations in English, based on existing coarse-grained annotations. Your goal is to break down each coarse action into a sequence of precise sub-actions that capture the key manipulations in the video.

Instructions:
Review the provided coarse-grained annotations and the video title carefully.

For each coarse annotation with its time interval, create fine-grained annotations that describe the sequence of actions occurring within that interval.

Sub-Action Granularity (General Guideline):

For short coarse actions (≤5 seconds in duration): keep original actions.
For medium coarse actions (5-10 seconds): Aim for 2-4 fine-grained sub-actions.
For long coarse actions (≥10 seconds): Aim for 4-5 fine-grained sub-actions.
This is a general guideline; the actual number of sub-actions should be driven by the distinct manipulations observed, except for "FOLD" actions which have a specific requirement (see instruction 4).

Special Handling for "FOLD" Coarse Actions:

If the coarse-grained annotation describes a "fold" action (e.g., "[00:12 - 00:18] fold the flattened black shirt..."):
    The number of *actual folding sub-actions* you identify and output MUST be between 2 and 4. These sub-actions should represent distinct, significant folding motions that contribute to the final folded state of the garment.
    Crucially, actions performed during any initial 'flattening' or 'spreading out' phase, even if they involve temporary overlapping of fabric (e.g., smoothing out a sleeve by laying it over the body of the shirt *before* the main folding process begins), are NOT considered 'folds' for this counting purpose.
    A 'fold' sub-action, for counting purposes, only begins *after* the garment has been fully spread out or flattened on a surface. The first counted fold is the first manipulation that intentionally and permanently reduces the garment's surface area after it has reached this flattened state.
    For each of these 2-4 folding sub-actions, you MUST specify the degree or extent of the fold. Use clear, quantified terms like:
        "fold the shirt in half (to 1/2)"
        "fold the left third of the shirt over the center (to 1/3)"
    Do NOT include other actions like 'grasp', 'lift', 'reposition', or 'smooth out' as separate sub-actions within this "fold" segment. These minor actions, if present, are considered integral parts of one of continuous folding motions. The focus is solely on the state change achieved by each of the 2-4 identified folds.

For each fine-grained action (including folds and other actions):

The specific action being performed (verb).
The object being manipulated with its current state/condition.
The starting location/position of the object.
The ending location/position of the object (if applicable).
Describe object states clearly (e.g., "crumpled shirt", "flattened shirt", "shirt folded to 1/2").
Specify locations precisely (e.g., "from the left side of the table", "towards the center", "into the storage bin").
Use present tense verb forms (e.g., "fold the shirt," not "folding the shirt").
Focus on object manipulation rather than just movement.
Capture all key actions including: grasping, lifting, moving, placing, folding (as per instruction 4), unfolding, flattening, spreading, transferring, and any other significant manipulations.
Be comprehensive: include every action that results in a meaningful change to the object's position, orientation, or state.
Be specific and descriptive but keep each action concise.
List each action on a separate line.
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line. These timestamps must be accurate and reflect the actual start and end of the fine-grained sub-action within the video segment defined by the coarse annotation. Ensure the sub-action timestamps are sequential and collectively cover the coarse action's duration.
Do not include any numbering, bullets, or additional formatting beyond the specified [MM:SS - MM:SS] description.
Only output the action descriptions, nothing else.

Video Title:
{title}

Coarse-grained Annotations (Input for this task):
{coarse_input}

Expected Fine-grained Output Format:
[MM:SS - MM:SS] fine-grained action 1 (e.g., grasp the crumpled shirt from the pile)
[MM:SS - MM:SS] fine-grained action 2 (e.g., move the crumpled shirt to the center of the table)
[MM:SS - MM:SS] fine-grained action 3 (e.g., flatten the crumpled shirt on the table)
[MM:SS - MM:SS] fine-grained action 4 (e.g., fold the right half of the flattened shirt over the left half to 1/2)
[MM:SS - MM:SS] fine-grained action 5 (e.g., fold the top half of the half-folded shirt down to the bottom half to 1/4)
...
"""
    
    # 设置默认提示词
    default_prompt = PROMPTS.get("default", PROMPTS[list(PROMPTS.keys())[0]] if PROMPTS else None)
    if default_prompt:
        print_safe("已设置默认提示词")
    else:
        print_safe("错误: 无法设置默认提示词，程序将退出")
        sys.exit(1)
    
    # 获取要处理的基础路径（可以是命令行参数或直接指定）
    if len(sys.argv) > 1:
        base_path = sys.argv[1]
        print_safe(f"从命令行参数获取处理路径: {base_path}")
    else:
        # 默认处理的视频文件夹
        base_path = video_base_path
        print_safe(f"使用默认处理路径: {base_path}")
    
    # 模型名称
    model_name = VIDEO_CONFIG['model_name']
    
    print_safe("=" * 50)
    print_safe(f"开始递归处理目录: {base_path}")
    print_safe(f"使用 {len(CLIENTS)} 个API客户端并行处理")
    print_safe(f"最大并发数: {VIDEO_CONFIG['max_workers']}")
    print_safe("=" * 50)
    
    processed_folders = process_directory_recursively(
        base_path,           # 要处理的基础路径 
        log_folder,          # 结果保存文件夹
        standard_base_path,  # 标准结果JSON文件的基础路径
        default_prompt,      # 默认提示词
        video_base_path,     # 视频文件的基础路径，用于生成相对路径结构
        model_name           # 模型名称
    )
    print_safe("=" * 50)
    print_safe(f"处理完成，共处理了 {processed_folders} 个包含视频的文件夹")
    print_safe("=" * 50) 