import cv2
import json
import re
import os
import base64
import shutil
from datetime import datetime
import numpy as np
from openai import OpenAI
from typing import Optional
from enum import Enum
import time
import math
class Strategy(Enum):
    CONSTANT_INTERVAL = "constant_interval"
    EVEN_INTERVAL = "even_interval"
    CONSTANT_INTERVAL_WITH_END = "constant_interval_with_end"

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 如果是EVEN_INTERVAL策略，根据视频时长动态调整max_frames
    if extraction_strategy == Strategy.EVEN_INTERVAL:
        # 视频时长以5为单位向上取整
        # adjusted_max_frames = int(math.ceil(duration / 5) * 5)
        # print(f"视频时长: {duration:.2f}秒, 调整后的max_frames: {adjusted_max_frames}")
        # max_frames = adjusted_max_frames
        frame_interval = int(length / max_frames)
    elif extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    else:
        raise ValueError("Invalid extraction strategy")
    
    frame_count = 0
    keyframes = []
    timestamps = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(image_path, frame)
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    print("sampled frames:", len(keyframes))
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    system_prompt = """You are a video action annotation specialist with expertise in analyzing and labeling human actions in videos. Your task is to carefully observe the video frames and provide detailed, accurate annotations of the actions being performed.

Key responsibilities:
1. Identify and label key actions with precise timing
2. Focus on significant manipulations and state changes
3. Provide clear, concise descriptions of actions
4. Maintain consistent formatting in your output
5. Ensure temporal accuracy in your annotations

Please follow the specific instructions provided in the user prompt for each video analysis task."""
    
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "system",
            "content": system_prompt,
        },
        {
            "role": "user",
            "content": content,
        }
    ]

def parse_result(result_str):
    pattern = r"\[(\d{2}):(\d{2}) - (\d{2}):(\d{2})\] (.+)"
    actions = []
    for line in result_str.strip().split('\n'):
        m = re.match(pattern, line)
        if m:
            start_min, start_sec, end_min, end_sec, action = m.groups()
            start_time = int(start_min) * 60 + int(start_sec)
            end_time = int(end_min) * 60 + int(end_sec)
            actions.append({
                "start": start_time,
                "end": end_time,
                "action": action
            })
    return actions

def put_text_with_background(img, text, position, font_scale=0.1, thickness=1, 
                           text_color=(255, 255, 255), bg_color=(0, 0, 0), 
                           padding=5, font=cv2.FONT_HERSHEY_SIMPLEX):
    (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
    x, y = position
    bg_rect = (x - padding, y - text_height - padding, 
               text_width + 2*padding, text_height + 2*padding)
    overlay = img.copy()
    cv2.rectangle(overlay, 
                 (bg_rect[0], bg_rect[1]), 
                 (bg_rect[0] + bg_rect[2], bg_rect[1] + bg_rect[3]), 
                 bg_color, -1)
    cv2.addWeighted(overlay, 0.7, img, 0.3, 0, img)
    cv2.putText(img, text, (x, y), font, font_scale, text_color, thickness, cv2.LINE_AA)

def process_and_visualize_video(video_path: str, output_path: str, model_name: str = "doubao-seed-1-6-thinking-250615", json_path: Optional[str] = None):
    # 设置提示词
    prompt = """
You are a video action annotation specialist. Your task is to identify and label key actions performed in the given video with detailed information about objects, their states, and locations in english, focusing on coarse-grained annotations.

ABSOLUTELY CRITICAL INSTRUCTION: Your primary goal is to determine the PRECISE start and end timestamps for each action based solely on visual evidence in the video. Do NOT use any default timings, do NOT divide the video into equal segments, and do NOT assume actions have a fixed duration. Each video is unique.

Instructions:
Watch the video frame by frame if necessary to pinpoint exact moments. For clothing folding tasks, reference these typical 2 coarse-grained steps:

Fold the clothing item
Place the folded clothing item into the destination (e.g., bin)
CRITICAL TIMING GUIDELINES FOR ACTIONS - OBSERVE VISUALLY:

Action 1 (Fold the clothing item):
Start: The very first moment hands make significant contact with the clothing item with the clear intent to initiate the folding process. This includes any initial spreading, smoothing, or arranging of the item on the workspace if these actions are an integral and continuous part of preparing for and executing the first fold. The action begins with the first manipulation aimed at systematically reducing the item's surface area by creating creases.
If an item is picked up from a pile and immediately folded (in the air or on a surface without extensive prior flattening), the 'Fold' start is when the first folding motion begins on that item.
The timestamp must mark the beginning of the actual first folding manipulation itself that intends to reduce the garment's dimensions.
End: The moment all folding motions for this item are complete, and the item is in its final folded state, just before it is lifted for placement.
Action 2 (Place):
Start: The moment the fully folded item is clearly lifted from the work surface (or from the hands if folded in the air) with the intent to move it to its destination.
End: The moment the item is released and settled at its final destination (e.g., in the bin, on a pile).
For each action, include the following elements:
The specific action being performed (verb from the list above: Fold, Place)
The object being manipulated with its current state/condition
The starting location/position of the object
The ending location/position of the object
Describe object states clearly (e.g., "crumpled towel", "folded towel")
Specify locations precisely (e.g., "from the corner of the board", "to the center", "into the bin")
Use present tense verb forms (e.g., "fold the paper" not "folding the paper")
Focus on object manipulation rather than just movement
Focus on main, coarse-grained actions that represent key steps in the task - avoid overly detailed sub-actions
Be specific and descriptive but keep each action concise
List each action on a separate line
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line, where MM:SS are the start and end timestamps (in minutes and seconds) indicating when the action starts and ends in the video (e.g., [00:12 - 00:18])
Do not include any numbering, bullets, or additional formatting
Only output the action descriptions, nothing else
Additionally, provide a single sentence at the beginning that summarizes the overall task being performed in the video as the title

Output Format:
Title: [One sentence description of the overall task in the video]
[MM:SS - MM:SS] action 1
[MM:SS - MM:SS] action 2
"""
    
    # 创建临时帧文件夹
    temp_frames_dir = os.path.join(os.path.dirname(output_path), "temp_frames")
    if os.path.exists(temp_frames_dir):
        shutil.rmtree(temp_frames_dir)
    os.makedirs(temp_frames_dir)
    
    try:
        # 采样视频帧
        sampling_fps = 1
        max_frames = 20
        sampling_interval = 1.0 / sampling_fps
        sampling_start_time = time.time()
        selected_images, timestamps = preprocess_video(
            video_file_path=video_path,
            output_dir=temp_frames_dir,
            extraction_strategy=Strategy.EVEN_INTERVAL,
            interval_in_seconds=sampling_interval,
            use_timestamp=True,
            max_frames=max_frames
        )
        sampling_time = time.time() - sampling_start_time
        print(f"采样时间: {sampling_time:.2f}秒，共采样{len(selected_images)}帧")
        # 构建消息并调用API
        message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
        client = OpenAI(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="46359fe6-6163-49cc-a2a4-ea223091ea0a",
        )
        inference_start_time = time.time()
        response = client.chat.completions.create(
            model=model_name,
            messages=message
        )
        inference_time = time.time() - inference_start_time
        
        # 打印token使用情况
        print(f"Input tokens: {response.usage.prompt_tokens}")
        print(f"Output tokens: {response.usage.completion_tokens}")
        print(f"Total tokens: {response.usage.total_tokens}")
        print(f"模型推理时间: {inference_time:.2f}秒")
        # 获取结果并解析
        result = response.choices[0].message.content
        actions = parse_result(result)
        
        # 追加结果到json文件
        if json_path is not None:
            result_entry = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "filename": os.path.basename(video_path),
                "prompt": prompt,
                "result": result.strip(),
                "model": model_name
            }
            if os.path.exists(json_path):
                with open(json_path, 'r', encoding='utf-8') as f:
                    try:
                        data = json.load(f)
                        if not isinstance(data, list):
                            data = []
                    except Exception:
                        data = []
            else:
                data = []
            data.append(result_entry)
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 打开视频
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"错误：无法打开视频文件 {video_path}")
            return

        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        frame_idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            cur_time = frame_idx / fps
            
            # 查找当前时间对应的action
            action_text = ""
            for act in actions:
                if act['start'] <= cur_time <= act['end']:
                    action_text = act['action']
                    break

            # 叠加action
            if action_text:
                put_text_with_background(
                    frame, 
                    action_text, 
                    (20, height - 20),
                    font_scale=0.4,
                    text_color=(255, 255, 255),
                    bg_color=(0, 0, 0)
                )
            
            # 在左上角显示model名称
            put_text_with_background(
                frame, 
                f"Model: {model_name}", 
                (20, 30),
                font_scale=0.4,
                text_color=(255, 255, 255),
                bg_color=(0, 0, 0)
            )

            out.write(frame)
            frame_idx += 1

        cap.release()
        out.release()
        print(f"Done! 输出文件: {output_path}")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_frames_dir):
            shutil.rmtree(temp_frames_dir)

if __name__ == "__main__":
    # 示例使用
    video_path = r"E:\midea\data\video_v2\1\flod\collection_1748251935_top.mp4"
    timestamp_str = time.strftime("%Y%m%d_%H%M%S")
    output_path = fr"E:\midea\seed_annotion\video\standard\standard_shirts_{timestamp_str}.mp4"
    json_path = r"E:\midea\data_annotion\model_api\seed\results\seed_results_standard.json"
    process_and_visualize_video(video_path, output_path, json_path=json_path)