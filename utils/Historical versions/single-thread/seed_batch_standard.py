from typing import Optional, Dict
from enum import Enum
import os
import base64
import shutil
import json
from datetime import datetime
import math
import cv2
import numpy as np
from openai import OpenAI
import sys

# Video processing configuration
VIDEO_CONFIG = {
    'sampling_fps': 1,
    'max_frames': 20,
    'sampling_interval': 1.0,  # 1.0 / sampling_fps
    'model_name': "doubao-1-5-thinking-vision-pro-250428"
}

# Initialize OpenAI client once to be reused
OPENAI_CLIENT = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key="3fe079be-2fa4-493e-bf9c-eec8e5315f48",
)

# Dictionary to store prompts
PROMPTS = {}

def load_prompts_from_directory(prompts_dir: str) -> Dict[str, str]:
    """Load prompts from text files in the specified directory.
    
    Args:
        prompts_dir (str): Path to directory containing prompt files
        
    Returns:
        Dict[str, str]: Dictionary mapping folder names to prompts
    """
    prompts = {}
    
    if not os.path.exists(prompts_dir):
        print(f"Warning: Prompts directory {prompts_dir} does not exist")
        return prompts
    
    # Load each prompt file
    for filename in os.listdir(prompts_dir):
        if filename.endswith(".txt"):
            folder_name = os.path.splitext(filename)[0]
            file_path = os.path.join(prompts_dir, filename)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    prompt_text = f.read()
                prompts[folder_name] = prompt_text
                print(f"Loaded prompt for folder type: {folder_name}")
            except Exception as e:
                print(f"Error loading prompt file {filename}: {str(e)}")
    
    return prompts

class Strategy(Enum):
    # sampling strategies
    CONSTANT_INTERVAL = "constant_interval"  # sampling at a constant interval, fps sampling
    EVEN_INTERVAL = "even_interval"  # sampling at an even interval, uniform sampling

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # 计算视频时长(秒)
    duration = length / fps
    
    # 如果是EVEN_INTERVAL策略，根据视频时长动态调整max_frames
    if extraction_strategy == Strategy.EVEN_INTERVAL:
        # 视频时长以5为单位向上取整
        # adjusted_max_frames = int(math.ceil(duration / 5) * 5)
        # print(f"视频时长: {duration:.2f}秒, 调整后的max_frames: {adjusted_max_frames}")
        # max_frames = adjusted_max_frames
        frame_interval = int(length / max_frames)
    elif extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    else:
        raise ValueError("Invalid extraction strategy")
    
    frame_count = 0
    keyframes = []
    timestamps = []
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(image_path, frame)
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    print("sampled frames:", len(keyframes))
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    """Construct messages for the video understanding"""
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            # add timestamp for each frame
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "user",
            "content": content,
        }
    ]

def convert_to_json_format(text_output: str, fps: float = 50, video_filename: str = "") -> dict:
    """Convert model output text to JSON format with frame indices.
    
    Args:
        text_output (str): Model output text
        fps (float): Frames per second of the video
        video_filename (str): Name of the video file without extension
        
    Returns:
        dict: JSON format data with frame indices
    """
    try:
        # Split the text into lines and remove empty lines
        lines = [line.strip() for line in text_output.strip().split('\n') if line.strip()]
        
        if not lines:
            print("Error: Empty input text")
            return {}
            
        # Extract title
        title = lines[0].replace('Title: ', '').strip()
        
        # Process action lines
        actions = []
        for line in lines[1:]:
            try:
                # Check if line has proper timestamp format
                if '[' not in line or ']' not in line:
                    print(f"Warning: Line does not have proper timestamp format: {line}")
                    continue
                
                # Extract time range and action description
                time_range = line[line.find('[')+1:line.find(']')]
                action_desc = line[line.find(']')+1:].strip()
                
                # Skip if action description is empty
                if not action_desc:
                    print(f"Warning: Empty action description for time range [{time_range}]")
                    continue
                
                # Convert MM:SS format to seconds
                def time_to_seconds(time_str):
                    minutes, seconds = map(int, time_str.split(':'))
                    return minutes * 60 + seconds
                
                # Check if time range has proper format
                if ' - ' not in time_range:
                    print(f"Warning: Time range does not have proper format: {time_range}")
                    continue
                
                # Convert time to frame indices
                start_time, end_time = time_range.split(' - ')
                start_seconds = time_to_seconds(start_time)
                end_seconds = time_to_seconds(end_time)
                
                # Ensure end time is greater than start time
                if end_seconds <= start_seconds:
                    print(f"Warning: End time {end_time} is not greater than start time {start_time}")
                    continue
                
                start_frame = int(start_seconds * fps)
                end_frame = int(end_seconds * fps)
                
                actions.append({
                    'start_frame': start_frame,
                    'end_frame': end_frame,
                    'description': action_desc
                })
            except Exception as e:
                print(f"Error processing action line: {line}")
                print(f"Error details: {str(e)}")
                continue
        
        # Create the result dictionary
        result = {
            'video_name': video_filename,
            'title': title,
            'actions': actions,
            'raw_output': text_output  # Add raw model output
        }
        
        print(f"Converted result: {json.dumps(result, indent=2)}")  # Debug print
        return result
        
    except Exception as e:
        print(f"Error converting to JSON format: {str(e)}")
        print(f"Input text: {text_output}")  # Debug print
        return {}

def check_and_adjust_action3(result_text: str) -> str:
    """Check if the placement action (last action) exceeds 2 seconds and adjust timestamps accordingly.
    
    This function is designed to handle cases where the first action (Flatten/spread) might not exist.
    It identifies actions based on their descriptions rather than fixed indices.
    
    Args:
        result_text (str): Model output text
        
    Returns:
        str: Adjusted model output text
    """
    lines = [line.strip() for line in result_text.strip().split('\n') if line.strip()]
    
    if len(lines) < 3:  # Need at least title + 2 actions (fold and place)
        return result_text
    
    title = lines[0]
    action_lines = lines[1:]  # All lines after title are action lines
    
    # Find the last two actions (should be fold and place)
    if len(action_lines) < 2:
        print("Warning: Expected at least 2 actions (fold and place), but found fewer")
        return result_text
    
    # The last action should be the placement action
    place_action = action_lines[-1]
    fold_action = action_lines[-2]
    
    # Extract time ranges
    def extract_time_range(action_line):
        if '[' not in action_line or ']' not in action_line:
            print(f"Warning: Action line does not have proper timestamp format: {action_line}")
            return None, None
        time_range = action_line[action_line.find('[')+1:action_line.find(']')]
        if ' - ' not in time_range:
            print(f"Warning: Time range does not have proper format: {time_range}")
            return None, None
        start_time, end_time = time_range.split(' - ')
        return start_time, end_time
    
    # Convert MM:SS format to seconds
    def time_to_seconds(time_str):
        if ':' not in time_str:
            print(f"Warning: Time string not in MM:SS format: {time_str}")
            return 0
        minutes, seconds = map(int, time_str.split(':'))
        return minutes * 60 + seconds
    
    # Convert seconds to MM:SS format
    def seconds_to_time(seconds):
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{minutes:02d}:{seconds:02d}"
    
    try:
        # Parse placement action times
        place_start, place_end = extract_time_range(place_action)
        if place_start is None or place_end is None:
            print("Warning: Could not extract time range from placement action")
            return result_text
            
        place_start_seconds = time_to_seconds(place_start)
        place_end_seconds = time_to_seconds(place_end)
        
        # Check if placement action exceeds 2 seconds
        place_duration = place_end_seconds - place_start_seconds
        
        if place_duration > 2:
            print(f"Placement action duration ({place_duration} seconds) exceeds 2 seconds. Adjusting timestamps...")
            
            # Parse fold action times
            fold_start, fold_end = extract_time_range(fold_action)
            if fold_start is None or fold_end is None:
                print("Warning: Could not extract time range from fold action")
                return result_text
                
            fold_start_seconds = time_to_seconds(fold_start)
            
            # Adjust fold action end and placement action start
            new_place_start_seconds = place_end_seconds - 2  # Make placement action exactly 2 seconds
            new_fold_end_seconds = new_place_start_seconds
            
            # Convert back to MM:SS format
            new_fold_end = seconds_to_time(new_fold_end_seconds)
            new_place_start = seconds_to_time(new_place_start_seconds)
            
            # Create new action lines
            new_fold_action = fold_action.replace(fold_end, new_fold_end)
            new_place_action = place_action.replace(place_start, new_place_start)
            
            # Replace in the result
            action_lines[-2] = new_fold_action
            action_lines[-1] = new_place_action
            
            # Reconstruct the full text
            adjusted_result = title + '\n' + '\n'.join(action_lines)
            
            print(f"Adjusted result: {adjusted_result}")
            return adjusted_result
        
        return result_text
        
    except Exception as e:
        print(f"Error adjusting placement action duration: {str(e)}")
        return result_text

def save_folder_results(log_folder, folder_path, results, base_video_path=None):
    """Save all results from a single folder to a dedicated JSON file.
    
    Args:
        log_folder (str): Base folder to save results
        folder_path (str): Full path to the folder being processed
        results (dict): Dictionary of results for all videos in the folder
        base_video_path (str): 视频文件的基础路径，用于生成相对路径结构
        
    Returns:
        str: Path to the saved JSON file
    """
    # 解析视频所在的文件夹路径结构
    # 比如 E:\midea\data\video_v2\1\fold 应该保存为 E:\midea\seed_annotion\results\1\fold\fold.json
    
    # 如果未指定base_video_path，则使用默认的视频基础路径
    if base_video_path is None:
        base_video_path = r"E:\midea\data\video_v2"  # 默认视频基础路径
    
    if folder_path.startswith(base_video_path):
        # 如果路径以base_video_path开头，获取相对路径部分
        relative_path = os.path.relpath(folder_path, base_video_path)
    else:
        # 如果不是以base_video_path开头，直接使用文件夹名作为相对路径
        relative_path = os.path.basename(folder_path)
    
    # 构建目标文件夹路径
    target_folder = os.path.join(log_folder, relative_path)
    
    # 创建目标文件夹
    if not os.path.exists(target_folder):
        os.makedirs(target_folder)
    
    # 获取当前文件夹的上一级文件夹名称作为JSON文件名
    # 如果当前是根目录，则使用当前文件夹名
    folder_parts = os.path.normpath(relative_path).split(os.sep)
    if len(folder_parts) > 1:
        # 使用当前文件夹名（即最后一个部分）作为文件名
        json_filename = folder_parts[-1]
    else:
        # 只有一级目录，使用它作为文件名
        json_filename = folder_parts[0]
    
    # 创建结果文件路径 (使用文件夹名作为JSON文件名)
    result_file = os.path.join(target_folder, f"{json_filename}.json")
    
    # 保存结果
    with open(result_file, "w", encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"Saved folder results to {result_file}")
    return result_file

def save_result(log_folder, filename, prompt, result, model_name, folder_path, results_dict, actual_fps=30.0):
    """Save the analysis result to a results dictionary for batch saving later.
    
    Args:
        log_folder (str): Folder to save results
        filename (str): Name of the processed file
        prompt (str): Prompt used for analysis
        result (str): Raw result from the model
        model_name (str): Name of the model used
        folder_path (str): Path to the folder containing the video
        results_dict (dict): Dictionary to collect results for the current folder
        actual_fps (float): Actual FPS of the video, defaults to 30.0
        
    Returns:
        dict: Updated results dictionary
    """
    # 获取视频文件名（不带后缀）
    video_name = os.path.splitext(filename)[0]
    
    print(f"Processing result for {video_name}")
    
    # 转换结果为JSON格式
    json_result = convert_to_json_format(result, video_filename=video_name, fps=actual_fps)
    
    if not json_result:
        print(f"Warning: Empty result for {video_name}")
        return results_dict
    
    # 添加到结果字典
    results_dict[video_name] = json_result
    
    return results_dict

def process_single_video(video_path, log_folder, prompt, folder_path, results_dict, model_name=VIDEO_CONFIG['model_name']):
    """Process a single video file and add its result to the results dictionary.
    
    Args:
        video_path (str): Path to the video file
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        folder_path (str): Path to the folder containing the video
        results_dict (dict): Dictionary to collect results for the current folder
        model_name (str): Model name to use
        
    Returns:
        dict: Updated results dictionary
    """
    if not os.path.exists(video_path):
        print(f"错误: 指定的视频文件不存在: {video_path}")
        return results_dict
        
    filename = os.path.basename(video_path)
    print(f"处理视频: {filename}")
    
    try:
        # 获取视频的实际FPS
        cap = cv2.VideoCapture(video_path)
        actual_fps = cap.get(cv2.CAP_PROP_FPS)
        cap.release()
        
        # 检查FPS是否有效，如果无效则使用默认值
        if actual_fps <= 0 or math.isnan(actual_fps):
            actual_fps = 50
            print(f"警告: 无法检测到有效的FPS，使用默认值: {actual_fps}")
        else:
            print(f"视频FPS: {actual_fps}")
        
        # 创建临时帧文件夹
        temp_frames_dir = os.path.join(log_folder, "temp_frames")
        if os.path.exists(temp_frames_dir):
            shutil.rmtree(temp_frames_dir)
        
        # 采样视频帧
        selected_images, timestamps = preprocess_video(
            video_file_path=video_path,
            output_dir=temp_frames_dir,
            extraction_strategy=Strategy.EVEN_INTERVAL,
            interval_in_seconds=VIDEO_CONFIG['sampling_interval'],
            use_timestamp=True,
            max_frames=VIDEO_CONFIG['max_frames']
        )
        
        # 构建消息并调用API
        message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
        response = OPENAI_CLIENT.chat.completions.create(
            model=model_name,
            messages=message
        )
        
        # 保存结果
        result = response.choices[0].message.content
        
        # 检查并调整action3的时长（确保不超过2秒）
        # result = check_and_adjust_action3(result)
        
        # 将结果添加到字典中
        results_dict = save_result(log_folder, filename, prompt, result, model_name, folder_path, results_dict, actual_fps)
        
        # 清理临时文件
        if os.path.exists(temp_frames_dir):
            shutil.rmtree(temp_frames_dir)
        
        return results_dict
        
    except Exception as e:
        print(f"处理视频 {filename} 时发生错误: {str(e)}")
        return results_dict

def process_videos_in_folder(folder_path, log_folder, prompt, model_name=VIDEO_CONFIG['model_name']):
    """Process all videos in a folder and save results to a single JSON file.
    
    Args:
        folder_path (str): Path to the folder containing videos
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        model_name (str): Model name to use
        
    Returns:
        bool: True if videos were processed, False otherwise
    """
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 指定的文件夹不存在: {folder_path}")
        return False
        
    # 获取文件夹中的所有文件和子文件夹
    items = os.listdir(folder_path)
    
    # 筛选视频文件
    video_files = [f for f in items if os.path.isfile(os.path.join(folder_path, f)) and 
                   f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
    
    # 如果当前文件夹有视频文件，处理它们
    if video_files:
        print(f"\n处理文件夹: {folder_path}")
        print(f"发现 {len(video_files)} 个视频文件")
        
        # 用于收集当前文件夹中所有视频的结果
        folder_results = {}
        
        # 遍历处理视频文件
        for filename in video_files:
            video_path = os.path.join(folder_path, filename)
            folder_results = process_single_video(video_path, log_folder, prompt, folder_path, folder_results, model_name)
        
        # 保存当前文件夹的所有结果到一个JSON文件
        if folder_results:
            save_folder_results(log_folder, folder_path, folder_results)
            print(f"文件夹 {folder_path} 中的 {len(folder_results)} 个视频处理完成")
            return True
        else:
            print(f"文件夹 {folder_path} 中没有成功处理的视频")
            return False
    else:
        # 当前文件夹没有视频文件，但可能有子文件夹
        print(f"文件夹 {folder_path} 中没有找到视频文件，检查子文件夹...")
        return False

def should_process_folder(folder_name: str) -> bool:
    """Check if a folder should be processed based on its name.
    
    Criteria: Folder name must contain 'folding' but not 'random'.
    
    Args:
        folder_name (str): Name of the folder to check
        
    Returns:
        bool: True if the folder should be processed, False otherwise
    """
    folder_name_lower = folder_name.lower()
    
    # Check if the folder name contains 'folding' but not 'random'
    return 'folding' in folder_name_lower and 'random' not in folder_name_lower

def process_directory_recursively(base_path, log_folder, default_prompt, base_video_path=None, model_name=VIDEO_CONFIG['model_name']):
    """递归处理目录及其所有子目录中的视频文件。
    
    Args:
        base_path (str): 要处理的基础路径
        log_folder (str): 结果保存文件夹
        default_prompt (str): 默认视频分析提示词
        base_video_path (str): 视频文件的基础路径，用于生成相对路径结构，默认与base_path相同
        model_name (str): 模型名称
        
    Returns:
        int: 处理的文件夹数量
    """
    processed_folders_count = 0
    
    # 如果未指定base_video_path，则使用base_path
    if base_video_path is None:
        base_video_path = base_path
    
    # 检查基础路径是否存在
    if not os.path.exists(base_path):
        print(f"错误: 指定的路径不存在: {base_path}")
        return processed_folders_count
    
    # 如果是单个视频文件，直接处理它
    if os.path.isfile(base_path) and base_path.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
        folder_path = os.path.dirname(base_path)
        folder_name = os.path.basename(folder_path)
        
        # 选择适合该文件夹的prompt
        prompt = select_prompt_for_folder(folder_name, default_prompt)
        
        results_dict = {}
        results_dict = process_single_video(base_path, log_folder, prompt, folder_path, results_dict, model_name)
        if results_dict:
            save_folder_results(log_folder, folder_path, results_dict, base_video_path)
            processed_folders_count += 1
        return processed_folders_count
    
    # 获取目录中的所有文件和文件夹
    for root, dirs, files in os.walk(base_path):
        # 检查当前目录中是否有视频文件
        video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
        
        # 只有当文件夹中有视频文件时，才进行进一步的处理
        if video_files:
            # 获取当前文件夹名称
            folder_name = os.path.basename(root)
            
            # 检查文件夹名是否符合筛选条件
            if not should_process_folder(folder_name):
                print(f"跳过文件夹 {root}，因为不符合筛选条件（需包含'folding'但不包含'random'）")
                continue
                
            print(f"\n处理文件夹: {root}")
            print(f"发现 {len(video_files)} 个视频文件")
            
            # 选择适合该文件夹的prompt
            prompt = select_prompt_for_folder(folder_name, default_prompt)
            print(f"使用文件夹 '{folder_name}' 对应的提示词")
            
            # 用于收集当前文件夹中所有视频的结果
            folder_results = {}
            
            # 处理当前文件夹中的所有视频
            for video_file in video_files:
                video_path = os.path.join(root, video_file)
                folder_results = process_single_video(video_path, log_folder, prompt, root, folder_results, model_name)
            
            # 保存当前文件夹的所有结果到一个JSON文件
            if folder_results:
                save_folder_results(log_folder, root, folder_results, base_video_path)
                processed_folders_count += 1
                print(f"文件夹 {root} 中的 {len(folder_results)} 个视频处理完成")
    
    return processed_folders_count

def select_prompt_for_folder(folder_name: str, default_prompt: str) -> str:
    """根据文件夹名称选择适当的prompt
    
    Args:
        folder_name (str): 文件夹名称
        default_prompt (str): 默认prompt
        
    Returns:
        str: 选择的prompt
    """
    folder_name_lower = folder_name.lower()
    
    # 首先处理特定任务类型
    if "random_folding" in folder_name_lower:
        # random_folding 是最具体的，所以先检查它
        if "random_folding" in PROMPTS:
            return PROMPTS["random_folding"]
    elif "folding" in folder_name_lower:
        # 然后检查 folding
        if "folding" in PROMPTS:
            return PROMPTS["folding"]
    elif "picking" in folder_name_lower:
        # 最后检查 picking
        if "picking" in PROMPTS:
            return PROMPTS["picking"]
    
    # 尝试精确匹配（如果上面的匹配失败）
    if folder_name in PROMPTS:
        return PROMPTS[folder_name]
    
    # 尝试部分匹配（保留原有逻辑作为备用）
    for key in PROMPTS:
        if key in folder_name_lower or folder_name_lower in key:
            return PROMPTS[key]
    
    # 没有匹配到，使用默认prompt
    print(f"未找到文件夹 '{folder_name}' 对应的提示词，使用默认提示词")
    return default_prompt

# 主函数
if __name__ == "__main__":
    # 指定结果保存文件夹
    log_folder = r"E:\midea\seed_annotion\results_standard"
    
    # 指定保存提示词的文件夹
    prompts_folder = r"E:\midea\seed_annotion\prompt\standard"
    
    # 加载提示词
    PROMPTS = load_prompts_from_directory(prompts_folder)
    
    # 设置默认提示词
    default_prompt = """
You are a video action annotation specialist. Your task is to identify and label key actions performed in the given video with detailed information about objects, their states, and locations in english, focusing on coarse-grained annotations.

**ABSOLUTELY CRITICAL INSTRUCTION: Your primary goal is to determine the PRECISE start and end timestamps for each action based *solely* on visual evidence in the video. Do NOT use any default timings, do NOT divide the video into equal segments, and do NOT assume actions have a fixed duration. Each video is unique.**

Instructions:
Watch the video frame by frame if necessary to pinpoint exact moments. For clothing folding tasks, reference these typical 3 coarse-grained steps (note: flatten/spread may not always be present or significant for all items):
1. Flatten/spread the clothing item out
2. Fold the clothing item
3. Place the folded clothing item into the destination (e.g., bin)

**CRITICAL TIMING GUIDELINES FOR ACTIONS - OBSERVE VISUALLY:**
*   **Action 1 (Flatten/spread):**
    *   **Start:** The very first moment hands make significant contact with the clothing item with the clear intent to spread or flatten it, increasing its surface area on the workspace.
    *   **End:** The moment the item is fully spread out to its maximum intended extent for the current step, achieving a relatively stable flat state, AND hands are beginning to release from this spreading action or are repositioning for the next action (folding). **Any temporary overlapping of fabric parts *during* this flattening/spreading process (e.g., smoothing a sleeve by laying it over the body while still adjusting overall flatness) is considered part of this 'Flatten/spread' action.**
*   **Action 2 (Fold the clothing item):**
    *   **Start:** **This is the most critical timestamp and requires careful distinction from the 'Flatten/spread' phase.** The 'Fold' action begins *ONLY AFTER* the item has been fully spread/flattened (if a flattening step was performed) and is lying in a stable, flat state. The 'Fold' start timestamp is *EXACTLY* when the first *intentional and distinct* physical folding motion initiates on this *already flattened* item. This is characterized by a deliberate movement of one part of the fabric over another to create a permanent crease and systematically reduce the item's surface area.
        *   **Crucially, do NOT mistake actions from the 'Flatten/spread' phase for a 'Fold'.** For example, if a sleeve is laid over the body of a shirt *while the shirt is still being adjusted, smoothed, or spread out to achieve its initial full flatness*, this is still part of 'Flatten/spread', not the beginning of 'Fold'.
        *   **The 'Fold' action commences with the first manipulation that aims to reduce the garment's dimensions *after* it has achieved its stable, fully spread-out state.**
        *   **DO NOT start this timestamp if hands are merely hovering, repositioning after flattening, or if the item is not yet in its fully flattened preparatory state. The timestamp must mark the beginning of the *actual first fold itself on the properly prepared, flattened garment*.**
    *   **End:** The moment all folding motions for this item are complete, and the item is in its final folded state, just before it is lifted for placement.
*   **Action 3 (Place):**
    *   **Start:** The moment the fully folded item is clearly lifted from the work surface with the intent to move it to its destination.
    *   **End:** The moment the item is released and settled at its final destination (e.g., in the bin, on a pile).

For each action, include the following elements:
The specific action being performed (verb from the list above)
The object being manipulated with its current state/condition
The starting location/position of the object
The ending location/position of the object
Describe object states clearly (e.g., "crumpled towel", "folded towel", "flattened towel")
Specify locations precisely (e.g., "from the corner of the board", "to the center", "into the bin")
Use present tense verb forms (e.g., "fold the paper" not "folding the paper")
Focus on object manipulation rather than just movement
Focus on main, coarse-grained actions that represent key steps in the task - avoid overly detailed sub-actions
Be specific and descriptive but keep each action concise
List each action on a separate line
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line, where MM:SS are the start and end timestamps (in minutes and seconds) indicating when the action starts and ends in the video (e.g., [00:12 - 00:18])
Do not include any numbering, bullets, or additional formatting
Only output the action descriptions, nothing else
Additionally, provide a single sentence at the beginning that summarizes the overall task being performed in the video as the title

Output Format:
Title: [One sentence description of the overall task in the video]
[MM:SS - MM:SS] action 1
[MM:SS - MM:SS] action 2
[MM:SS - MM:SS] action 3
"""
    
    # 如果没有加载到任何提示词，使用默认提示词
    if not PROMPTS:
        print("警告: 未加载到任何提示词文件，将使用默认提示词")
        PROMPTS["default"] = default_prompt
    
    # 获取要处理的基础路径（可以是命令行参数或直接指定）
    if len(sys.argv) > 1:
        base_path = sys.argv[1]
    else:
        # 默认处理的视频文件夹
        base_path = r"E:\midea\data\video_v2\1"
    
    # 视频基础路径，用于生成相对路径结构
    base_video_path = r"E:\midea\data\video_v2\1"
    
    print(f"开始递归处理目录: {base_path}")
    processed_folders = process_directory_recursively(base_path, log_folder, default_prompt, base_video_path)
    print(f"\n处理完成，共处理了 {processed_folders} 个包含视频的文件夹") 