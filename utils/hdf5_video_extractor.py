import h5py
import cv2
import numpy as np
import os
from pathlib import Path

def print_hdf5_structure(hdf5_path):
    """
    Print the structure of an HDF5 file
    
    Args:
        hdf5_path (str): Path to the HDF5 file
    """
    try:
        with h5py.File(hdf5_path, 'r') as f:
            def print_attrs(name, obj):
                print(f"\nDataset/Group: {name}")
                print(f"Type: {type(obj)}")
                if isinstance(obj, h5py.Dataset):
                    print(f"Shape: {obj.shape}")
                    print(f"Data type: {obj.dtype}")
                print("Attributes:")
                for key, value in obj.attrs.items():
                    print(f"  {key}: {value}")
                print("-" * 50)
            
            print(f"\nStructure of HDF5 file: {hdf5_path}")
            print("=" * 50)
            f.visititems(print_attrs)
            
    except Exception as e:
        print(f"Error reading HDF5 file: {str(e)}")

def extract_top_view_video(hdf5_path, output_path):
    """
    Extract top view video from HDF5 file and save as MP4
    
    Args:
        hdf5_path (str): Path to the HDF5 file
        output_path (str): Path to save the output MP4 file
        
    Returns:
        str: HDF5 filename without extension
    """
    try:
        # Get HDF5 filename without extension
        hdf5_filename = os.path.splitext(os.path.basename(hdf5_path))[0]
        
        # Open HDF5 file
        with h5py.File(hdf5_path, 'r') as f:
            # Get the top view video data from the correct path
            top_view_data = f['observations/images/cam_high'][:]
            
            # Get video properties
            num_frames = top_view_data.shape[0]
            # Get the first frame to determine video dimensions
            first_frame = cv2.imdecode(top_view_data[0], 1)
            height, width = first_frame.shape[:2]
            fps = 50  # Default FPS, adjust if needed
            
            # Create video writer
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
            
            # Write frames
            for frame in top_view_data:
                # Decode the compressed frame
                frame_decoded = cv2.imdecode(frame, 1)
                if frame_decoded is None:
                    print(f"Warning: Failed to decode frame")
                    continue
                out.write(frame_decoded)
            
            # Release video writer
            out.release()
            print(f"Successfully saved video to {output_path}")
            
        return hdf5_filename
            
    except Exception as e:
        print(f"Error processing HDF5 file: {str(e)}")
        return None

def process_directory(input_dir, output_dir):
    """
    Process all HDF5 files in a directory
    
    Args:
        input_dir (str): Directory containing HDF5 files
        output_dir (str): Directory to save output MP4 files
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Process all HDF5 files in the input directory
    for hdf5_file in Path(input_dir).glob('*.hdf5'):
        output_path = os.path.join(output_dir, f"{hdf5_file.stem}_top.mp4")
        extract_top_view_video(str(hdf5_file), output_path)

if __name__ == "__main__":
    # Example usage
    input_directory = "/Users/<USER>/Documents/data/vla_data/hdf5_data/random_folding_pants_zjx_20250529_compressed"  # Replace with your input directory
    output_directory = "/Users/<USER>/Documents/data/vla_data/video_data/random_folding_pants_zjx_20250529_compressed"  # Replace with your output directory
    
    # First print the structure of the first HDF5 file found
    hdf5_files = list(Path(input_directory).glob('*.hdf5'))
    if hdf5_files:
        print_hdf5_structure(str(hdf5_files[0]))
    
    # Then process all files
    process_directory(input_directory, output_directory) 