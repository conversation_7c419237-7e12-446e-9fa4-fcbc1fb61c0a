from typing import Optional
from enum import Enum
import os
import base64
import shutil
import json
from datetime import datetime
from pathlib import Path
import h5py
import cv2
import numpy as np
from openai import OpenAI

class Strategy(Enum):
    # sampling strategies
    CONSTANT_INTERVAL = "constant_interval"  # sampling at a constant interval, fps sampling
    EVEN_INTERVAL = "even_interval"  # sampling at an even interval, uniform sampling

def preprocess_video(
        video_file_path: str,
        output_dir: str,
        extraction_strategy: Optional[Strategy] = Strategy.EVEN_INTERVAL,
        interval_in_seconds: Optional[float] = 1,
        max_frames: Optional[int] = 10,
        use_timestamp: bool = True,
        keyframe_naming_template: str = "frame_{:04d}.jpg",
) -> tuple[list[str], Optional[list[float]]]:
    """Sampling videos and extract keyframes with different strategies.
    Args:
        video_file_path (str): video path
        output_dir (str): output directory for sampled keyframes
        extraction_strategy (Optional[Strategy], optional): extraction strategy. Defaults to Strategy.EVEN_INTERVAL.
        interval_in_seconds (Optional[float], optional): the sampling interval
        max_frames (Optional[int], optional): maximum number of sampled frames. Defaults to 10.
        use_timestamp (bool): whether to output video timestamps. Defaults to True.
        keyframe_naming_template (_type_, optional): keyframe naming template. Defaults to "frame_{:04d}.jpg".
    Returns:
        list[str]: sampled keyframe paths
        list[float]: timestamps of sampled keyframes
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    cap = cv2.VideoCapture(video_file_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    length = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    if extraction_strategy == Strategy.CONSTANT_INTERVAL:
        frame_interval = int(fps * interval_in_seconds)
    elif extraction_strategy == Strategy.EVEN_INTERVAL:
        frame_interval = int(length / max_frames)
    else:
        raise ValueError("Invalid extraction strategy")
    frame_count = 0
    keyframes = []
    timestamps = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        if frame_count % frame_interval == 0:
            image_path = os.path.join(
                output_dir, keyframe_naming_template.format(len(keyframes))
            )
            cv2.imwrite(
                image_path,
                frame,
            )
            keyframes.append(image_path)
            timestamps.append(round(frame_count / fps, 1))
        frame_count += 1
        if len(keyframes) >= max_frames:
            break

    print("sampled frames:", len(keyframes))
    if use_timestamp:
        return keyframes, timestamps
    return keyframes, None

def resize(image):
    height, width = image.shape[:2]
    if height < width:
        target_height, target_width = 480, 640
    else:
        target_height, target_width = 640, 480
    if height <= target_height and width <= target_width:
        return image
    if height / target_height < width / target_width:
        new_width = target_width
        new_height = int(height * (new_width / width))
    else:
        new_height = target_height
        new_width = int(width * (new_height / height))
    return cv2.resize(image, (new_width, new_height))

def encode_image(image_path: str) -> str:
    image = cv2.imread(image_path)
    image_resized = resize(image)
    _, encoded_image = cv2.imencode(".jpg", image_resized)
    return base64.b64encode(encoded_image).decode("utf-8")

def construct_messages(image_paths: list[str], timestamps: list[float], prompt: str) -> list[dict]:
    """Construct messages for the video understanding"""
    content = []
    for idx, image_path in enumerate(image_paths):
        if timestamps is not None:
            # add timestamp for each frame
            content.append({
                "type": "text",
                "text": f'[{timestamps[idx]} second]'
            })
        content.append(
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{encode_image(image_path)}",
                    "detail": "low"
                },
            }
        )
    content.append(
        {
            "type": "text",
            "text": prompt,
        })
    return [
        {
            "role": "user",
            "content": content,
        }
    ]

def save_result(log_folder, filename, prompt, result, model_name):
    # 创建结果文件夹
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)
    
    # 创建结果字典
    result_data = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "filename": filename,
        "prompt": prompt,
        "result": result.strip(),  # 确保结果没有多余的空行
        "model": model_name
    }
    
    # 主结果文件路径
    main_result_file = os.path.join(log_folder, "seed_results.json")
    
    # 读取现有结果
    existing_results = []
    if os.path.exists(main_result_file):
        try:
            with open(main_result_file, "r", encoding='utf-8') as f:
                existing_results = json.load(f)
        except json.JSONDecodeError:
            existing_results = []
    
    # 添加新结果
    existing_results.append(result_data)
    
    # 保存更新后的结果
    with open(main_result_file, "w", encoding='utf-8') as f:
        json.dump(existing_results, f, ensure_ascii=False, indent=2)
    
    return main_result_file

def process_single_video(video_path: str, log_folder: str, prompt: str, model_name: str = "doubao-1-5-thinking-vision-pro-250428") -> dict:
    """Process a single video file and return the result.
    
    Args:
        video_path (str): Path to the video file
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        model_name (str): Model name to use
        
    Returns:
        dict: Dictionary containing the result
    """
    if not os.path.exists(video_path):
        print(f"错误: 指定的视频文件不存在: {video_path}")
        return {}
        
    filename = os.path.basename(video_path)
    print(f"处理视频: {filename}")
    
    try:
        # 创建临时帧文件夹
        temp_frames_dir = os.path.join(log_folder, "temp_frames")
        if os.path.exists(temp_frames_dir):
            shutil.rmtree(temp_frames_dir)
        
        # 采样视频帧
        sampling_fps = 1
        max_frames = 30
        sampling_interval = 1.0 / sampling_fps
        selected_images, timestamps = preprocess_video(
            video_file_path=video_path,
            output_dir=temp_frames_dir,
            extraction_strategy=Strategy.EVEN_INTERVAL,
            interval_in_seconds=sampling_interval,
            use_timestamp=True,
            max_frames=max_frames
        )
        
        # 构建消息并调用API
        message = construct_messages(image_paths=selected_images, timestamps=timestamps, prompt=prompt)
        client = OpenAI(
            base_url="https://ark.cn-beijing.volces.com/api/v3",
            api_key="5dc6b723-dedc-4375-b306-2da43c81cb88",
        )
        response = client.chat.completions.create(
            model=model_name,
            messages=message
        )
        
        # 保存结果
        result = response.choices[0].message.content
        results = {filename: result}
        
        # 保存到文件
        output_file = save_result(log_folder, filename, prompt, result, model_name)
        print(f"已保存结果到: {output_file}")
        
        # 清理临时文件
        if os.path.exists(temp_frames_dir):
            shutil.rmtree(temp_frames_dir)
        
        return results
        
    except Exception as e:
        print(f"处理视频 {filename} 时发生错误: {str(e)}")
        return {}

def process_videos_in_folder(folder_path: str, log_folder: str, prompt: str, model_name: str = "doubao-1-5-thinking-vision-pro-250428") -> dict:
    """Process all videos in a folder.
    
    Args:
        folder_path (str): Path to the folder containing videos
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        model_name (str): Model name to use
        
    Returns:
        dict: Dictionary containing results for all videos
    """
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"错误: 指定的文件夹不存在: {folder_path}")
        return {}
        
    # 获取文件夹中的所有文件
    files = os.listdir(folder_path)
    
    # 筛选视频文件
    video_files = [f for f in files if f.lower().endswith(('.mp4', '.avi', '.mov', '.mkv'))]
    
    if not video_files:
        print("没有找到符合条件的视频文件")
        return {}
    
    results = {}
    
    # 遍历视频文件
    for filename in video_files:
        video_path = os.path.join(folder_path, filename)
        result = process_single_video(video_path, log_folder, prompt, model_name)
        results.update(result)
    
    return results

def process_videos(input_path: str, log_folder: str, prompt: str, model_name: str = "doubao-1-5-thinking-vision-pro-250428") -> dict:
    """Process videos from either a single file or a folder.
    
    Args:
        input_path (str): Path to either a video file or a folder containing videos
        log_folder (str): Folder to save results
        prompt (str): Prompt for video analysis
        model_name (str): Model name to use
        
    Returns:
        dict: Dictionary containing results for all processed videos
    """
    if os.path.isfile(input_path):
        # 如果是单个视频文件
        return process_single_video(input_path, log_folder, prompt, model_name)
    elif os.path.isdir(input_path):
        # 如果是文件夹
        return process_videos_in_folder(input_path, log_folder, prompt, model_name)
    else:
        print(f"错误: 指定的路径不存在: {input_path}")
        return {}

def get_fps(main_folder, subfolder):
    digits = ''.join(filter(str.isdigit, subfolder))
    date = int(digits[:8])
    if len(digits) >= 8 and date > 20250603:
        return 50
    else:
        if main_folder in ["mobile_aloha_4_wheels", "mobile_aloha_3_wheels"]:
            return 15
        elif main_folder == "static_aloha":
            return 50
    return 50

def auto_detect_fps(h5_path, default_fps=30, session_name="", task_name=""):
    try:
        with h5py.File(h5_path, 'r') as f:
            if "fps" in f.attrs:
                return int(f.attrs["fps"])
            if "meta" in f and "fps" in f["meta"]:
                return int(f["meta/fps"][()])
            if "timestamps" in f:
                ts = f["timestamps"][:]
                if len(ts) > 1:
                    interval = np.diff(ts)
                    mean_interval = np.mean(interval)
                    if mean_interval > 0:
                        return int(round(1.0 / mean_interval))
    except Exception as e:
        print(f"[WARN] Cannot extract FPS from {h5_path}: {e}")
    return get_fps(session_name, task_name) or default_fps

def extract_top_view_video(hdf5_path, output_path, fps):
    """
    Extract top view video from HDF5 and save as MP4.
    """
    try:
        with h5py.File(hdf5_path, 'r') as f:
            top_view_data = f['observations/images/cam_high'][:]
            first_frame = cv2.imdecode(top_view_data[0], 1)
            height, width = first_frame.shape[:2]

            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

            for frame in top_view_data:
                frame_decoded = cv2.imdecode(frame, 1)
                if frame_decoded is not None:
                    frame_decoded = cv2.cvtColor(frame_decoded,cv2.COLOR_RGB2BGR)
                    out.write(frame_decoded)
                else:
                    print(f"[WARN] Failed to decode a frame in {hdf5_path}")
            out.release()
            print(f"[INFO] Saved video to: {output_path}")
    except Exception as e:
        print(f"[ERROR] Failed to process {hdf5_path}: {e}")

def process_all_hdf5(input_root, output_dir):
    """
    Recursively process all HDF5 files under input_root and save as videos in output_dir.
    """
    input_root = Path(input_root)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    for hdf5_file in input_root.rglob("*.hdf5"):
        parts = hdf5_file.parts
        try:
            main_folder = parts[-3]
            subfolder = parts[-2]
            filename_wo_ext = hdf5_file.stem
        except IndexError:
            print(f"[WARN] Path format not correct: {hdf5_file}")
            continue

        fps = auto_detect_fps(str(hdf5_file), session_name=main_folder, task_name=subfolder)
        output_filename = f"{main_folder}_{subfolder}_{filename_wo_ext}.mp4"
        output_path = output_dir / output_filename

        extract_top_view_video(str(hdf5_file), str(output_path), fps)

# 主函数
if __name__ == "__main__":
    #转hdf5成MP4
    base_path = r"/mnt/NAS-164/volume1/robot-data-nas3/compressed_data"
    output_path = r"/home/<USER>/liuxin/cqh/seed-api/164v3"
    output_dir = os.path.join(output_path, "output_164_videos")
    os.makedirs(output_dir, exist_ok=True)
    process_all_hdf5(base_path, output_dir)

    # 指定结果保存文件夹
    log_folder = r"/home/<USER>/liuxin/cqh/seed-api/164v"
    os.makedirs(log_folder, exist_ok=True)
    # 设置提示词
    prompt = """
You are a video action annotation specialist. Your task is to identify and label every single key action performed in the given video with detailed information about objects, their states, and locations in english.

Instructions:
Watch the video carefully and identify each and every distinct key action performed throughout the entire video - do not skip any significant manipulation or state change
For each action, include the following elements:
The specific action being performed (verb)
The object being manipulated with its current state/condition
The starting location/position of the object
The ending location/position of the object (if applicable)
Describe object states clearly (e.g., "crumpled towel", "folded towel", "flattened towel")
Specify locations precisely (e.g., "from the corner of the board", "to the center", "into the bin")
Use present tense verb forms (e.g., "fold the paper" not "folding the paper")
Focus on object manipulation rather than just movement
Describe the state of the manipulated object in detail using numerical and quantified terms, such as folded in 1/2
Capture all key actions including: grasping, lifting, moving, placing, folding, unfolding, flattening, spreading, transferring, and any other significant manipulations
Be comprehensive - include every action that results in a meaningful change to the object's position, orientation, or state
Be specific and descriptive but keep each action concise
List each action on a separate line
For each action, prepend the corresponding time interval in the format [MM:SS - MM:SS] at the beginning of the line, where MM:SS are the start and end timestamps (in minutes and seconds) indicating when the action starts and ends in the video (e.g., [00:12 - 00:18])
Do not include any numbering, bullets, or additional formatting
Only output the action descriptions, nothing else

Output Format:
[MM:SS - MM:SS] action 1
[MM:SS - MM:SS] action 2
[MM:SS - MM:SS] action 3
......
......
......
"""
    
    # ##示例：处理单个视频文件
    # single_video_path = r"E:\midea\tiny-vla-v2\model_api\source_video\top_camera-images-rgb.mp4"  # 替换为实际的视频文件路径
    # single_result = process_videos(single_video_path, log_folder, prompt)
    # print(f"\n处理单个视频完成: {len(single_result)} 个结果")
    
    # 示例：处理文件夹中的所有视频
    video_folder = output_dir
    #video_folder = r"E:\midea\tiny-vla-v2\model_api\seed\split_videos_20250528_131828"  # 替换为实际的视频文件夹路径
    #folder_results = process_videos(video_folder, log_folder, prompt)
    #print(f"\n处理文件夹完成，共处理了 {len(folder_results)} 个视频文件")
