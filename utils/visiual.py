import cv2
import json
import re
import os
import argparse
from pathlib import Path

def parse_result(result_str):
    # 匹配 [MM:SS - MM:SS] action
    pattern = r"\[(\d{2}):(\d{2}) - (\d{2}):(\d{2})\] (.+)"
    actions = []
    for line in result_str.strip().split('\n'):
        m = re.match(pattern, line)
        if m:
            start_min, start_sec, end_min, end_sec, action = m.groups()
            start_time = int(start_min) * 60 + int(start_sec)
            end_time = int(end_min) * 60 + int(end_sec)
            actions.append({
                "start": start_time,
                "end": end_time,
                "action": action
            })
    return actions

def get_model_abbr(model_name):
    """从完整模型名称中提取简称"""
    # 移除版本号和日期
    model_name = re.sub(r'-\d{6}$', '', model_name)
    # 提取主要部分
    parts = model_name.split('-')
    if len(parts) >= 2:
        # 取前两个部分作为简称
        return '-'.join(parts[:2])
    return model_name

def put_text_with_background(img, text, position, font_scale=0.1, thickness=1, 
                           text_color=(255, 255, 255), bg_color=(0, 0, 0), 
                           padding=5, font=cv2.FONT_HERSHEY_SIMPLEX):
    """在图片上添加带背景的文字"""
    # 获取文字大小
    (text_width, text_height), baseline = cv2.getTextSize(text, font, font_scale, thickness)
    
    # 计算背景矩形的位置和大小
    x, y = position
    bg_rect = (x - padding, y - text_height - padding, 
               text_width + 2*padding, text_height + 2*padding)
    
    # 绘制半透明背景
    overlay = img.copy()
    cv2.rectangle(overlay, 
                 (bg_rect[0], bg_rect[1]), 
                 (bg_rect[0] + bg_rect[2], bg_rect[1] + bg_rect[3]), 
                 bg_color, -1)
    cv2.addWeighted(overlay, 0.7, img, 0.3, 0, img)
    
    # 绘制文字
    cv2.putText(img, text, (x, y), font, font_scale, text_color, thickness, cv2.LINE_AA)

def process_video(video_path, annotation_data, output_dir, model_name=None):
    """处理单个视频文件"""
    video_name = os.path.basename(video_path)
    name_without_ext = os.path.splitext(video_name)[0]
    
    # 解析标注数据
    if model_name is None:
        model_name = "Unknown Model"
    
    # 获取模型简称
    model_abbr = get_model_abbr(model_name)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建输出文件名
    output_name = os.path.join(output_dir, f"{model_abbr}_{name_without_ext}.mp4")
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"错误：无法打开视频文件 {video_path}")
        return False
    
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_name, fourcc, fps, (width, height))
    
    frame_idx = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        cur_time = frame_idx / fps
        
        # 查找当前时间对应的action
        action_text = ""
        for act in annotation_data:
            if act['start'] <= cur_time <= act['end']:
                action_text = act['action']
                break
        
        # 叠加action和model，使用较小的字体
        if action_text:
            # 在底部显示action，使用半透明背景
            put_text_with_background(
                frame, 
                action_text, 
                (20, height - 20),  # 位置调整到左下角
                font_scale=0.4,     # 更小的字体
                text_color=(255, 255, 255),  # 白色文字
                bg_color=(0, 0, 0)  # 黑色背景
            )
        
        # 在左上角显示model名称
        put_text_with_background(
            frame, 
            f"Model: {model_name}", 
            (20, 30),  # 位置调整到左上角
            font_scale=0.4,  # 更小的字体
            text_color=(255, 255, 255),  # 白色文字
            bg_color=(0, 0, 0)  # 黑色背景
        )
        
        out.write(frame)
        frame_idx += 1
    
    cap.release()
    out.release()
    print(f"完成处理: {output_name}")
    return True

def main():
    # 添加命令行参数解析
    parser = argparse.ArgumentParser(description='为视频添加标注')
    parser.add_argument('--json', type=str, default=r"E:\midea\seed_annotion\results_dense\1\flod\flod.json", help='标注JSON文件路径')
    parser.add_argument('--videos_dir', type=str, default=r"E:\midea\data\video_v2\1\flod", help='输入视频文件夹路径')
    parser.add_argument('--output_dir', type=str, default=r"E:\midea\seed_annotion\video\dense", help='输出视频文件夹路径')
    parser.add_argument('--model_name', type=str, default="doubao-1-5-thinking-vision-pro-250428", help='模型名称（可选）')
    parser.add_argument('--video_extension', type=str, default='.mp4', help='视频文件扩展名（默认.mp4）')
    args = parser.parse_args()

    # 检查文件夹是否存在
    if not os.path.isdir(args.videos_dir):
        print(f"错误：输入视频文件夹不存在 {args.videos_dir}")
        return
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 读取json文件
    with open(args.json, 'r', encoding='utf-8') as f:
        json_data = json.load(f)
    
    # 获取视频文件列表
    video_files = [f for f in os.listdir(args.videos_dir) if f.endswith(args.video_extension)]
    
    if not video_files:
        print(f"错误：在 {args.videos_dir} 中没有找到扩展名为 {args.video_extension} 的视频文件")
        return
    
    # 设置模型名称
    model_name = args.model_name
    
    # 处理每个视频
    processed_count = 0
    for video_file in video_files:
        video_name_without_ext = os.path.splitext(video_file)[0]
        
        # 查找视频对应的标注数据
        if video_name_without_ext in json_data:
            # 使用新的JSON格式
            video_data = json_data[video_name_without_ext]
            raw_output = video_data.get('raw_output', '')
            actions = parse_result(raw_output)
            
            # 处理视频
            video_path = os.path.join(args.videos_dir, video_file)
            if process_video(video_path, actions, args.output_dir, model_name):
                processed_count += 1
        else:
            print(f"警告：在JSON中找不到视频 {video_file} 的标注数据")
    
    print(f"批处理完成! 成功处理 {processed_count}/{len(video_files)} 个视频文件")

if __name__ == '__main__':
    main()